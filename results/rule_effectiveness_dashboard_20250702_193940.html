
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 19:39:41 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">-399.67%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">48,415</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">61.5%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.19</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>47.11%</strong></td>
                    <td>64.1%</td>
                    <td>2694</td>
                    <td>1.05</td>
                    <td class="negative">29.38%</td>
                    <td class="positive"><strong>0.9428</strong></td>
                    <td class="negative">+0.87% / -1.46%</td>
                    <td>2h24m<br><small>(1.0m - 31h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>34.91%</strong></td>
                    <td>63.3%</td>
                    <td>4142</td>
                    <td>1.02</td>
                    <td class="negative">29.87%</td>
                    <td class="positive"><strong>0.9484</strong></td>
                    <td class="negative">+0.88% / -1.46%</td>
                    <td>2h12m<br><small>(1.0m - 33h44m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="positive"><strong>6.16%</strong></td>
                    <td>73.9%</td>
                    <td>46</td>
                    <td>1.69</td>
                    <td class="neutral">2.69%</td>
                    <td class="positive"><strong>0.5923</strong></td>
                    <td class="negative">+0.85% / -1.40%</td>
                    <td>2h51m<br><small>(4.0m - 18h4m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>3.42%</strong></td>
                    <td>63.1%</td>
                    <td>2249</td>
                    <td>1.01</td>
                    <td class="negative">32.48%</td>
                    <td class="positive"><strong>0.8001</strong></td>
                    <td class="negative">+0.86% / -1.45%</td>
                    <td>2h34m<br><small>(1.0m - 34h54m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Acad Rule 3: Volatility Breakout</td>
                    <td class="positive"><strong>2.53%</strong></td>
                    <td>64.1%</td>
                    <td>78</td>
                    <td>1.13</td>
                    <td class="neutral">2.89%</td>
                    <td class="positive"><strong>0.5391</strong></td>
                    <td class="negative">+0.89% / -1.40%</td>
                    <td>4h0m<br><small>(1.0m - 18h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Momentum Rule 5: Momentum Breakout</td>
                    <td class="positive"><strong>1.83%</strong></td>
                    <td>100.0%</td>
                    <td>3</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+1.21% / 0.00%</td>
                    <td>3.3m<br><small>(1.0m - 5.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>SMC Rule 2: Fair Value Gap Fill</td>
                    <td class="positive"><strong>0.46%</strong></td>
                    <td>100.0%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.91% / 0.00%</td>
                    <td>1h7m</td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>New Rule 4: Ultimate Oscillator Breakout</td>
                    <td class="negative"><strong>0.00%</strong></td>
                    <td>0.0%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 1: MA Alignment with RSI Oversold</td>
                    <td class="negative"><strong>0.00%</strong></td>
                    <td>0.0%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="negative"><strong>-1.50%</strong></td>
                    <td>62.9%</td>
                    <td>4607</td>
                    <td>1.00</td>
                    <td class="negative">34.46%</td>
                    <td class="positive"><strong>0.8542</strong></td>
                    <td class="negative">+0.88% / -1.47%</td>
                    <td>2h7m<br><small>(1.0m - 33h45m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="negative"><strong>-1.56%</strong></td>
                    <td>53.8%</td>
                    <td>26</td>
                    <td>0.85</td>
                    <td class="negative">5.91%</td>
                    <td class="positive"><strong>0.3420</strong></td>
                    <td class="negative">+1.27% / -1.72%</td>
                    <td>4.2m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>SMC Rule 5: Institutional Candle Pattern</td>
                    <td class="negative"><strong>-4.48%</strong></td>
                    <td>54.3%</td>
                    <td>105</td>
                    <td>0.87</td>
                    <td class="negative">10.71%</td>
                    <td class="positive"><strong>0.4803</strong></td>
                    <td class="negative">+1.09% / -1.48%</td>
                    <td>52.0m<br><small>(1.0m - 12h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="negative"><strong>-11.06%</strong></td>
                    <td>62.3%</td>
                    <td>4475</td>
                    <td>0.99</td>
                    <td class="negative">42.82%</td>
                    <td class="positive"><strong>0.8147</strong></td>
                    <td class="negative">+0.87% / -1.44%</td>
                    <td>2h15m<br><small>(1.0m - 34h13m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="negative"><strong>-12.66%</strong></td>
                    <td>58.3%</td>
                    <td>309</td>
                    <td>0.85</td>
                    <td class="negative">14.97%</td>
                    <td class="positive"><strong>0.5499</strong></td>
                    <td class="negative">+0.87% / -1.41%</td>
                    <td>3h55m<br><small>(4.0m - 30h32m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="negative"><strong>-15.35%</strong></td>
                    <td>58.7%</td>
                    <td>448</td>
                    <td>0.88</td>
                    <td class="negative">23.24%</td>
                    <td class="positive"><strong>0.5759</strong></td>
                    <td class="negative">+0.87% / -1.41%</td>
                    <td>3h29m<br><small>(1.0m - 30h6m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="negative"><strong>-18.54%</strong></td>
                    <td>61.9%</td>
                    <td>2441</td>
                    <td>0.97</td>
                    <td class="negative">36.71%</td>
                    <td class="positive"><strong>0.7389</strong></td>
                    <td class="negative">+0.86% / -1.43%</td>
                    <td>2h45m<br><small>(1.0m - 31h9m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="negative"><strong>-20.64%</strong></td>
                    <td>61.9%</td>
                    <td>2148</td>
                    <td>0.96</td>
                    <td class="negative">34.06%</td>
                    <td class="positive"><strong>0.7226</strong></td>
                    <td class="negative">+0.86% / -1.43%</td>
                    <td>2h52m<br><small>(1.0m - 33h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="negative"><strong>-21.56%</strong></td>
                    <td>62.2%</td>
                    <td>2983</td>
                    <td>0.97</td>
                    <td class="negative">35.14%</td>
                    <td class="positive"><strong>0.7537</strong></td>
                    <td class="negative">+0.87% / -1.45%</td>
                    <td>2h39m<br><small>(1.0m - 34h13m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="negative"><strong>-22.13%</strong></td>
                    <td>61.4%</td>
                    <td>1698</td>
                    <td>0.94</td>
                    <td class="negative">37.88%</td>
                    <td class="positive"><strong>0.6857</strong></td>
                    <td class="negative">+0.86% / -1.43%</td>
                    <td>2h51m<br><small>(1.0m - 34h54m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="negative"><strong>-24.03%</strong></td>
                    <td>58.9%</td>
                    <td>810</td>
                    <td>0.88</td>
                    <td class="negative">25.61%</td>
                    <td class="positive"><strong>0.6114</strong></td>
                    <td class="negative">+0.88% / -1.41%</td>
                    <td>3h19m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="negative"><strong>-26.03%</strong></td>
                    <td>60.3%</td>
                    <td>1236</td>
                    <td>0.92</td>
                    <td class="negative">29.66%</td>
                    <td class="positive"><strong>0.6498</strong></td>
                    <td class="negative">+0.87% / -1.43%</td>
                    <td>3h12m<br><small>(1.0m - 34h9m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="negative"><strong>-28.48%</strong></td>
                    <td>59.6%</td>
                    <td>937</td>
                    <td>0.87</td>
                    <td class="negative">32.50%</td>
                    <td class="positive"><strong>0.5986</strong></td>
                    <td class="negative">+0.87% / -1.45%</td>
                    <td>3h9m<br><small>(1.0m - 29h27m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="negative"><strong>-30.82%</strong></td>
                    <td>61.3%</td>
                    <td>2861</td>
                    <td>0.96</td>
                    <td class="negative">39.58%</td>
                    <td class="positive"><strong>0.7171</strong></td>
                    <td class="negative">+0.87% / -1.43%</td>
                    <td>2h44m<br><small>(1.0m - 34h12m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="negative"><strong>-31.09%</strong></td>
                    <td>61.0%</td>
                    <td>2131</td>
                    <td>0.94</td>
                    <td class="negative">32.20%</td>
                    <td class="positive"><strong>0.6935</strong></td>
                    <td class="negative">+0.86% / -1.43%</td>
                    <td>2h49m<br><small>(1.0m - 31h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>Rule 28: Volume Breakout</td>
                    <td class="negative"><strong>-31.21%</strong></td>
                    <td>59.8%</td>
                    <td>1805</td>
                    <td>0.93</td>
                    <td class="negative">36.37%</td>
                    <td class="positive"><strong>0.6693</strong></td>
                    <td class="negative">+0.89% / -1.42%</td>
                    <td>2h39m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>Volatility Rule 2: ATR Expansion Signal</td>
                    <td class="negative"><strong>-31.36%</strong></td>
                    <td>59.1%</td>
                    <td>1185</td>
                    <td>0.89</td>
                    <td class="negative">33.89%</td>
                    <td class="positive"><strong>0.6206</strong></td>
                    <td class="negative">+0.89% / -1.42%</td>
                    <td>2h51m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#27</strong></td>
                    <td>Volume Rule 4: Volume Breakout Confirmation</td>
                    <td class="negative"><strong>-49.53%</strong></td>
                    <td>58.6%</td>
                    <td>1874</td>
                    <td>0.86</td>
                    <td class="negative">51.98%</td>
                    <td class="positive"><strong>0.5865</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h36m<br><small>(1.0m - 27h39m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#28</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="negative"><strong>-51.09%</strong></td>
                    <td>60.1%</td>
                    <td>3547</td>
                    <td>0.92</td>
                    <td class="negative">58.70%</td>
                    <td class="positive"><strong>0.6517</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>2h20m<br><small>(1.0m - 34h3m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#29</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="negative"><strong>-62.94%</strong></td>
                    <td>60.2%</td>
                    <td>3576</td>
                    <td>0.90</td>
                    <td class="negative">63.37%</td>
                    <td class="positive"><strong>0.6070</strong></td>
                    <td class="negative">+0.87% / -1.45%</td>
                    <td>2h33m<br><small>(1.0m - 34h51m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>SMC Rule 2: Fair Value Gap Fill</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">0.46%</td>
                    <td>1</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+0.91% / 0.00%</td>
                    <td>1h7m</td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Momentum Rule 5: Momentum Breakout</td>
                    <td class="positive"><strong>100.0%</strong></td>
                    <td class="positive">1.83%</td>
                    <td>3</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="positive">+1.21% / 0.00%</td>
                    <td>3.3m<br><small>(1.0m - 5.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 21: Gap Up</td>
                    <td class="positive"><strong>73.9%</strong></td>
                    <td class="positive">6.16%</td>
                    <td>46</td>
                    <td>1.69</td>
                    <td class="neutral">2.69%</td>
                    <td class="positive"><strong>0.5923</strong></td>
                    <td class="negative">+0.85% / -1.40%</td>
                    <td>2h51m<br><small>(4.0m - 18h4m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>64.1%</strong></td>
                    <td class="positive">47.11%</td>
                    <td>2694</td>
                    <td>1.05</td>
                    <td class="negative">29.38%</td>
                    <td class="positive"><strong>0.9428</strong></td>
                    <td class="negative">+0.87% / -1.46%</td>
                    <td>2h24m<br><small>(1.0m - 31h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Acad Rule 3: Volatility Breakout</td>
                    <td class="positive"><strong>64.1%</strong></td>
                    <td class="positive">2.53%</td>
                    <td>78</td>
                    <td>1.13</td>
                    <td class="neutral">2.89%</td>
                    <td class="positive"><strong>0.5391</strong></td>
                    <td class="negative">+0.89% / -1.40%</td>
                    <td>4h0m<br><small>(1.0m - 18h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>63.3%</strong></td>
                    <td class="positive">34.91%</td>
                    <td>4142</td>
                    <td>1.02</td>
                    <td class="negative">29.87%</td>
                    <td class="positive"><strong>0.9484</strong></td>
                    <td class="negative">+0.88% / -1.46%</td>
                    <td>2h12m<br><small>(1.0m - 33h44m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>63.1%</strong></td>
                    <td class="positive">3.42%</td>
                    <td>2249</td>
                    <td>1.01</td>
                    <td class="negative">32.48%</td>
                    <td class="positive"><strong>0.8001</strong></td>
                    <td class="negative">+0.86% / -1.45%</td>
                    <td>2h34m<br><small>(1.0m - 34h54m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>62.9%</strong></td>
                    <td class="negative">-1.50%</td>
                    <td>4607</td>
                    <td>1.00</td>
                    <td class="negative">34.46%</td>
                    <td class="positive"><strong>0.8542</strong></td>
                    <td class="negative">+0.88% / -1.47%</td>
                    <td>2h7m<br><small>(1.0m - 33h45m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>62.3%</strong></td>
                    <td class="negative">-11.06%</td>
                    <td>4475</td>
                    <td>0.99</td>
                    <td class="negative">42.82%</td>
                    <td class="positive"><strong>0.8147</strong></td>
                    <td class="negative">+0.87% / -1.44%</td>
                    <td>2h15m<br><small>(1.0m - 34h13m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>62.2%</strong></td>
                    <td class="negative">-21.56%</td>
                    <td>2983</td>
                    <td>0.97</td>
                    <td class="negative">35.14%</td>
                    <td class="positive"><strong>0.7537</strong></td>
                    <td class="negative">+0.87% / -1.45%</td>
                    <td>2h39m<br><small>(1.0m - 34h13m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>61.9%</strong></td>
                    <td class="negative">-20.64%</td>
                    <td>2148</td>
                    <td>0.96</td>
                    <td class="negative">34.06%</td>
                    <td class="positive"><strong>0.7226</strong></td>
                    <td class="negative">+0.86% / -1.43%</td>
                    <td>2h52m<br><small>(1.0m - 33h42m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>61.9%</strong></td>
                    <td class="negative">-18.54%</td>
                    <td>2441</td>
                    <td>0.97</td>
                    <td class="negative">36.71%</td>
                    <td class="positive"><strong>0.7389</strong></td>
                    <td class="negative">+0.86% / -1.43%</td>
                    <td>2h45m<br><small>(1.0m - 31h9m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>61.4%</strong></td>
                    <td class="negative">-22.13%</td>
                    <td>1698</td>
                    <td>0.94</td>
                    <td class="negative">37.88%</td>
                    <td class="positive"><strong>0.6857</strong></td>
                    <td class="negative">+0.86% / -1.43%</td>
                    <td>2h51m<br><small>(1.0m - 34h54m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>61.3%</strong></td>
                    <td class="negative">-30.82%</td>
                    <td>2861</td>
                    <td>0.96</td>
                    <td class="negative">39.58%</td>
                    <td class="positive"><strong>0.7171</strong></td>
                    <td class="negative">+0.87% / -1.43%</td>
                    <td>2h44m<br><small>(1.0m - 34h12m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>61.0%</strong></td>
                    <td class="negative">-31.09%</td>
                    <td>2131</td>
                    <td>0.94</td>
                    <td class="negative">32.20%</td>
                    <td class="positive"><strong>0.6935</strong></td>
                    <td class="negative">+0.86% / -1.43%</td>
                    <td>2h49m<br><small>(1.0m - 31h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Ext Rule 3: Bollinger Squeeze Breakout</td>
                    <td class="positive"><strong>60.3%</strong></td>
                    <td class="negative">-26.03%</td>
                    <td>1236</td>
                    <td>0.92</td>
                    <td class="negative">29.66%</td>
                    <td class="positive"><strong>0.6498</strong></td>
                    <td class="negative">+0.87% / -1.43%</td>
                    <td>3h12m<br><small>(1.0m - 34h9m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#17</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>60.2%</strong></td>
                    <td class="negative">-62.94%</td>
                    <td>3576</td>
                    <td>0.90</td>
                    <td class="negative">63.37%</td>
                    <td class="positive"><strong>0.6070</strong></td>
                    <td class="negative">+0.87% / -1.45%</td>
                    <td>2h33m<br><small>(1.0m - 34h51m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#18</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>60.1%</strong></td>
                    <td class="negative">-51.09%</td>
                    <td>3547</td>
                    <td>0.92</td>
                    <td class="negative">58.70%</td>
                    <td class="positive"><strong>0.6517</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>2h20m<br><small>(1.0m - 34h3m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#19</strong></td>
                    <td>Rule 28: Volume Breakout</td>
                    <td class="positive"><strong>59.8%</strong></td>
                    <td class="negative">-31.21%</td>
                    <td>1805</td>
                    <td>0.93</td>
                    <td class="negative">36.37%</td>
                    <td class="positive"><strong>0.6693</strong></td>
                    <td class="negative">+0.89% / -1.42%</td>
                    <td>2h39m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#20</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="positive"><strong>59.6%</strong></td>
                    <td class="negative">-28.48%</td>
                    <td>937</td>
                    <td>0.87</td>
                    <td class="negative">32.50%</td>
                    <td class="positive"><strong>0.5986</strong></td>
                    <td class="negative">+0.87% / -1.45%</td>
                    <td>3h9m<br><small>(1.0m - 29h27m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#21</strong></td>
                    <td>Volatility Rule 2: ATR Expansion Signal</td>
                    <td class="positive"><strong>59.1%</strong></td>
                    <td class="negative">-31.36%</td>
                    <td>1185</td>
                    <td>0.89</td>
                    <td class="negative">33.89%</td>
                    <td class="positive"><strong>0.6206</strong></td>
                    <td class="negative">+0.89% / -1.42%</td>
                    <td>2h51m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#22</strong></td>
                    <td>Ext Rule 5: ATR Volatility Expansion</td>
                    <td class="positive"><strong>58.9%</strong></td>
                    <td class="negative">-24.03%</td>
                    <td>810</td>
                    <td>0.88</td>
                    <td class="negative">25.61%</td>
                    <td class="positive"><strong>0.6114</strong></td>
                    <td class="negative">+0.88% / -1.41%</td>
                    <td>3h19m<br><small>(1.0m - 28h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#23</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>58.7%</strong></td>
                    <td class="negative">-15.35%</td>
                    <td>448</td>
                    <td>0.88</td>
                    <td class="negative">23.24%</td>
                    <td class="positive"><strong>0.5759</strong></td>
                    <td class="negative">+0.87% / -1.41%</td>
                    <td>3h29m<br><small>(1.0m - 30h6m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#24</strong></td>
                    <td>Volume Rule 4: Volume Breakout Confirmation</td>
                    <td class="positive"><strong>58.6%</strong></td>
                    <td class="negative">-49.53%</td>
                    <td>1874</td>
                    <td>0.86</td>
                    <td class="negative">51.98%</td>
                    <td class="positive"><strong>0.5865</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h36m<br><small>(1.0m - 27h39m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#25</strong></td>
                    <td>Volume Rule 3: Dark Pool Activity</td>
                    <td class="positive"><strong>58.3%</strong></td>
                    <td class="negative">-12.66%</td>
                    <td>309</td>
                    <td>0.85</td>
                    <td class="negative">14.97%</td>
                    <td class="positive"><strong>0.5499</strong></td>
                    <td class="negative">+0.87% / -1.41%</td>
                    <td>3h55m<br><small>(4.0m - 30h32m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#26</strong></td>
                    <td>SMC Rule 5: Institutional Candle Pattern</td>
                    <td class="positive"><strong>54.3%</strong></td>
                    <td class="negative">-4.48%</td>
                    <td>105</td>
                    <td>0.87</td>
                    <td class="negative">10.71%</td>
                    <td class="positive"><strong>0.4803</strong></td>
                    <td class="negative">+1.09% / -1.48%</td>
                    <td>52.0m<br><small>(1.0m - 12h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#27</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>53.8%</strong></td>
                    <td class="negative">-1.56%</td>
                    <td>26</td>
                    <td>0.85</td>
                    <td class="negative">5.91%</td>
                    <td class="positive"><strong>0.3420</strong></td>
                    <td class="negative">+1.27% / -1.72%</td>
                    <td>4.2m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#28</strong></td>
                    <td>New Rule 4: Ultimate Oscillator Breakout</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">0.00%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
                <tr>
                    <td><strong>#29</strong></td>
                    <td>Rule 1: MA Alignment with RSI Oversold</td>
                    <td class="neutral"><strong>0.0%</strong></td>
                    <td class="negative">0.00%</td>
                    <td>0</td>
                    <td>inf</td>
                    <td class="positive">0.00%</td>
                    <td class="positive"><strong>inf</strong></td>
                    <td class="negative">+0.00% / 0.00%</td>
                    <td>0m</td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['New Rule 4: Ultimate Oscillato...', 'Rule 1: MA Alignment with RSI ...', 'SMC Rule 2: Fair Value Gap Fil...', 'Momentum Rule 5: Momentum Brea...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 7: Bollinger Band Bounce', 'Rule 21: Gap Up', 'Acad Rule 3: Volatility Breako...', 'AI Rule 8: Momentum Divergence...', 'AI Rule 10: Composite Sentimen...', 'Acad Rule 2: Mean Reversion Fa...', 'SMC Rule 5: Institutional Cand...', 'Ext Rule 6: Fibonacci Support ...', 'Volume Rule 3: Dark Pool Activ...', 'Rule 2: Golden Cross'],
                y: [0, 0, 100.0, 100.0, 64.14253897550111, 63.32689521970063, 73.91304347826086, 64.1025641025641, 63.09470875944865, 62.88257000217061, 53.84615384615385, 54.285714285714285, 62.3463687150838, 58.252427184466015, 58.70535714285714],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['New Rule 4: Ultimate Oscillato...', 'Rule 1: MA Alignment with RSI ...', 'SMC Rule 2: Fair Value Gap Fil...', 'Momentum Rule 5: Momentum Brea...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 7: Bollinger Band Bounce', 'Rule 21: Gap Up', 'Acad Rule 3: Volatility Breako...', 'AI Rule 8: Momentum Divergence...', 'AI Rule 10: Composite Sentimen...', 'Acad Rule 2: Mean Reversion Fa...', 'SMC Rule 5: Institutional Cand...', 'Ext Rule 6: Fibonacci Support ...', 'Volume Rule 3: Dark Pool Activ...', 'Rule 2: Golden Cross'],
                y: [0.0, 0.0, np.float64(0.4572606382978739), np.float64(1.8289080742383668), np.float64(47.105837395561046), np.float64(34.91142227233253), np.float64(6.157558422241238), np.float64(2.5345556596370944), np.float64(3.4179764854375128), np.float64(-1.5045497032288746), np.float64(-1.559982369655244), np.float64(-4.4809133665246526), np.float64(-11.057208785591166), np.float64(-12.663380321116202), np.float64(-15.348940044350456)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
