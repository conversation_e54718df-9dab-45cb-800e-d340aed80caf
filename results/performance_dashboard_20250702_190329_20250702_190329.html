
<!DOCTYPE html>
<html>
<head>
    <title>Trading Rules Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .chart-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #34495e;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .positive {
            color: #27ae60;
            font-weight: bold;
        }
        .negative {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Trading Rules Performance Dashboard</h1>
        <p>Comprehensive Analysis of 8 Top-Performing Buy Rules</p>
        <p>Generated: 2025-07-02 19:03:29</p>
    </div>
    
    
    <div class="summary-stats">
        <div class="stat-card">
            <div class="stat-value">8</div>
            <div class="stat-label">Rules Passed Filters</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">50.0%</div>
            <div class="stat-label">Success Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">20.7%</div>
            <div class="stat-label">Average Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">53.8%</div>
            <div class="stat-label">Best Return</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">67.3%</div>
            <div class="stat-label">Average Win Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">2,600</div>
            <div class="stat-label">Total Trades</div>
        </div>
    </div>
        
    
    <div class="chart-container">
        <div class="chart-title">📊 Performance Overview</div>
        <div id="overview-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="overview-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("overview-chart")) {                    Plotly.newPlot(                        "overview-chart",                        [{"marker":{"color":["#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60","#27ae60"]},"text":["53.8%","39.8%","22.5%","22.8%","6.1%","7.9%","10.3%","2.3%"],"textposition":"auto","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","Rule 6: Stochastic Oversold Cross","Prof Rule 7: Mean Reversion Volatility Filter","Volume Rule 5: Smart Money Volume","Professional Rule 10: CCI Reversal Enhanced","Price Action Rule 3: Engulfing Pattern","Professional Rule 7: Chaikin Money Flow Reversal"],"y":[53.831686402051226,39.82902204159502,22.51441900210225,22.75850447781953,6.118398438251242,7.907000517575288,10.347921891884718,2.3015933631788212],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Total Return by Rule"},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🎯 Win/Loss Distribution</div>
        <div id="win-loss-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="win-loss-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("win-loss-chart")) {                    Plotly.newPlot(                        "win-loss-chart",                        [{"marker":{"color":"#27ae60"},"name":"Winning Trades","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","Rule 6: Stochastic Oversold Cross","Prof Rule 7: Mean Reversion Volatility Filter","Volume Rule 5: Smart Money Volume","Professional Rule 10: CCI Reversal Enhanced","Price Action Rule 3: Engulfing Pattern","Professional Rule 7: Chaikin Money Flow Reversal"],"y":[755,585,108,107,45,39,12,17],"type":"bar"},{"marker":{"color":"#e74c3c"},"name":"Losing Trades","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","Rule 6: Stochastic Oversold Cross","Prof Rule 7: Mean Reversion Volatility Filter","Volume Rule 5: Smart Money Volume","Professional Rule 10: CCI Reversal Enhanced","Price Action Rule 3: Engulfing Pattern","Professional Rule 7: Chaikin Money Flow Reversal"],"y":[435,341,48,49,24,22,3,10],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Win\u002fLoss Distribution"},"yaxis":{"title":{"text":"Number of Trades"}},"barmode":"stack","height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚖️ Risk vs Return Analysis</div>
        <div id="risk-return-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="risk-return-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("risk-return-chart")) {                    Plotly.newPlot(                        "risk-return-chart",                        [{"hovertemplate":"\u003cb\u003e%{text}\u003c\u002fb\u003e\u003cbr\u003eReturn: %{y:.1f}%\u003cbr\u003eMax Drawdown: %{x:.1f}%\u003cextra\u003e\u003c\u002fextra\u003e","marker":{"color":[53.831686402051226,39.82902204159502,22.51441900210225,22.75850447781953,6.118398438251242,7.907000517575288,10.347921891884718,2.3015933631788212],"colorbar":{"title":{"text":"Return (%)"}},"colorscale":[[0.0,"rgb(165,0,38)"],[0.1,"rgb(215,48,39)"],[0.2,"rgb(244,109,67)"],[0.3,"rgb(253,174,97)"],[0.4,"rgb(254,224,139)"],[0.5,"rgb(255,255,191)"],[0.6,"rgb(217,239,139)"],[0.7,"rgb(166,217,106)"],[0.8,"rgb(102,189,99)"],[0.9,"rgb(26,152,80)"],[1.0,"rgb(0,104,55)"]],"showscale":true,"size":10},"mode":"markers+text","text":["AI Rule 10","Rule 7","Rule 6","Prof Rule 7","Volume Rule 5","Professional Rule 10","Price Action Rule 3","Professional Rule 7"],"textposition":"top center","x":[32.19507808303527,40.61754810529939,9.734437355404241,12.12804621210469,6.523400025431305,6.716513042641008,2.301765630358884,6.064684224037023],"y":[53.831686402051226,39.82902204159502,22.51441900210225,22.75850447781953,6.118398438251242,7.907000517575288,10.347921891884718,2.3015933631788212],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Risk vs Return Analysis"},"xaxis":{"title":{"text":"Maximum Drawdown (%)"}},"yaxis":{"title":{"text":"Total Return (%)"}},"height":500},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">📈 Category Performance Comparison</div>
        <div id="category-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="category-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("category-chart")) {                    Plotly.newPlot(                        "category-chart",                        [{"marker":{"color":["#3498db","#9b59b6","#e67e22","#1abc9c"]},"text":["53.8%","31.2%","22.8%","6.7%"],"textposition":"auto","x":["AI_GENERATED","ORIGINAL","PROFESSIONAL","UNKNOWN"],"y":[53.831686402051226,31.171720521848634,22.75850447781953,6.668728552722517],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Average Return by Category"},"xaxis":{"title":{"text":"Rule Category"}},"yaxis":{"title":{"text":"Average Return (%)"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">🔄 Trade Frequency Analysis</div>
        <div id="frequency-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="frequency-chart" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("frequency-chart")) {                    Plotly.newPlot(                        "frequency-chart",                        [{"marker":{"color":"#3498db"},"text":["1190","926","156","156","69","61","15","27"],"textposition":"auto","x":["AI Rule 10: Composite Sentiment Reversal","Rule 7: Bollinger Band Bounce","Rule 6: Stochastic Oversold Cross","Prof Rule 7: Mean Reversion Volatility Filter","Volume Rule 5: Smart Money Volume","Professional Rule 10: CCI Reversal Enhanced","Price Action Rule 3: Engulfing Pattern","Professional Rule 7: Chaikin Money Flow Reversal"],"y":[1190,926,156,156,69,61,15,27],"type":"bar"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"title":{"text":"Trading Rules"},"tickangle":-45},"title":{"text":"Number of Trades per Rule"},"yaxis":{"title":{"text":"Number of Trades"}},"height":400,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">💰 Equity Curves - Top 5 Rules</div>
        <div id="equity-curves-chart"><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                            <div id="equity-curves-chart" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("equity-curves-chart")) {                    Plotly.newPlot(                        "equity-curves-chart",                        [{"line":{"color":"#e74c3c","width":2},"mode":"lines","name":"AI Rule 10","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190],"y":[0,0.045236711262227926,0.09047342252445585,0.13571013378668376,0.1809468450489117,0.2261835563111396,0.2714202675733675,0.31665697883559546,0.3618936900978234,0.4071304013600513,0.4523671126222792,0.49760382388450713,0.542840535146735,0.588077246408963,0.6333139576711909,0.6785506689334189,0.7237873801956468,0.7690240914578746,0.8142608027201026,0.8594975139823304,0.9047342252445584,0.9499709365067863,0.9952076477690143,1.0404443590312422,1.08568107029347,1.130917781555698,1.176154492817926,1.2213912040801538,1.2666279153423818,1.3118646266046097,1.3571013378668377,1.4023380491290656,1.4475747603912936,1.4928114716535212,1.5380481829157493,1.5832848941779771,1.6285216054402052,1.673758316702433,1.7189950279646609,1.764231739226889,1.8094684504891168,1.8547051617513448,1.8999418730135726,1.9451785842758007,1.9904152955380285,2.035652006800256,2.0808887180624844,2.1261254293247123,2.17136214058694,2.216598851849168,2.261835563111396,2.307072274373624,2.352308985635852,2.3975456968980797,2.4427824081603076,2.488019119422536,2.5332558306847637,2.5784925419469915,2.6237292532092193,2.668965964471447,2.7142026757336755,2.759439386995903,2.804676098258131,2.849912809520359,2.8951495207825872,2.9403862320448146,2.9856229433070425,3.0308596545692708,3.0760963658314986,3.1213330770937264,3.1665697883559543,3.2118064996181825,3.2570432108804104,3.302279922142638,3.347516633404866,3.392753344667094,3.4379900559293217,3.48322676719155,3.528463478453778,3.5737001897160057,3.6189369009782335,3.6641736122404613,3.7094103235026896,3.7546470347649175,3.7998837460271453,3.845120457289373,3.8903571685516014,3.9355938798138292,3.980830591076057,4.0260673023382845,4.071304013600512,4.116540724862741,4.161777436124969,4.207014147387197,4.2522508586494245,4.297487569911652,4.34272428117388,4.387960992436108,4.433197703698336,4.478434414960564,4.523671126222792,4.56890783748502,4.614144548747248,4.659381260009476,4.704617971271704,4.749854682533932,4.7950913937961595,4.840328105058387,4.885564816320615,4.930801527582843,4.976038238845072,5.0212749501072995,5.066511661369527,5.111748372631755,5.156985083893983,5.202221795156211,5.247458506418439,5.2926952176806665,5.337931928942894,5.383168640205123,5.428405351467351,5.473642062729579,5.518878773991806,5.5641154852540335,5.609352196516262,5.65458890777849,5.699825619040718,5.745062330302946,5.7902990415651745,5.835535752827402,5.880772464089629,5.926009175351857,5.971245886614085,6.016482597876314,6.0617193091385415,6.106956020400769,6.152192731662997,6.197429442925225,6.242666154187453,6.287902865449681,6.3331395767119085,6.378376287974136,6.423612999236365,6.468849710498593,6.514086421760821,6.559323133023049,6.604559844285276,6.649796555547504,6.695033266809732,6.74026997807196,6.785506689334188,6.830743400596416,6.875980111858643,6.921216823120871,6.9664535343831,7.011690245645328,7.056926956907556,7.1021636681697835,7.147400379432011,7.192637090694239,7.237873801956467,7.283110513218695,7.328347224480923,7.373583935743151,7.418820647005379,7.464057358267607,7.509294069529835,7.554530780792063,7.599767492054291,7.645004203316518,7.690240914578746,7.735477625840974,7.780714337103203,7.825951048365431,7.8711877596276585,7.916424470889886,7.961661182152114,8.006897893414342,8.052134604676569,8.097371315938798,8.142608027201025,8.187844738463253,8.233081449725482,8.27831816098771,8.323554872249938,8.368791583512165,8.414028294774393,8.45926500603662,8.504501717298849,8.549738428561076,8.594975139823305,8.640211851085533,8.68544856234776,8.73068527360999,8.775921984872216,8.821158696134445,8.866395407396672,8.9116321186589,8.956868829921127,9.002105541183356,9.047342252445585,9.092578963707812,9.13781567497004,9.183052386232267,9.228289097494496,9.273525808756723,9.318762520018952,9.363999231281179,9.409235942543408,9.454472653805635,9.499709365067863,9.544946076330092,9.590182787592319,9.635419498854548,9.680656210116775,9.725892921379003,9.77112963264123,9.816366343903459,9.861603055165686,9.906839766427915,9.952076477690143,9.99731318895237,10.042549900214599,10.087786611476826,10.133023322739055,10.178260034001282,10.22349674526351,10.268733456525737,10.313970167787966,10.359206879050195,10.404443590312422,10.44968030157465,10.494917012836877,10.540153724099106,10.585390435361333,10.630627146623562,10.675863857885789,10.721100569148016,10.766337280410246,10.811573991672473,10.856810702934702,10.902047414196929,10.947284125459158,10.992520836721384,11.037757547983611,11.08299425924584,11.128230970508067,11.173467681770298,11.218704393032525,11.263941104294753,11.30917781555698,11.354414526819209,11.399651238081436,11.444887949343663,11.490124660605892,11.535361371868118,11.580598083130349,11.625834794392576,11.671071505654805,11.716308216917032,11.761544928179259,11.806781639441487,11.852018350703714,11.897255061965943,11.94249177322817,11.9877284844904,12.032965195752627,12.078201907014856,12.123438618277083,12.16867532953931,12.213912040801539,12.259148752063766,12.304385463325994,12.349622174588221,12.39485888585045,12.440095597112679,12.485332308374906,12.530569019637134,12.575805730899361,12.62104244216159,12.666279153423817,12.711515864686046,12.756752575948273,12.801989287210501,12.84722599847273,12.892462709734957,12.937699420997186,12.982936132259413,13.028172843521642,13.073409554783868,13.118646266046097,13.163882977308324,13.209119688570553,13.254356399832782,13.299593111095009,13.344829822357237,13.390066533619464,13.435303244881693,13.48053995614392,13.525776667406149,13.571013378668376,13.616250089930604,13.661486801192831,13.70672351245506,13.751960223717287,13.797196934979514,13.842433646241743,13.887670357503973,13.9329070687662,13.978143780028429,14.023380491290656,14.068617202552884,14.113853913815111,14.15909062507734,14.204327336339567,14.249564047601796,14.294800758864023,14.340037470126251,14.385274181388478,14.430510892650707,14.475747603912934,14.520984315175161,14.56622102643739,14.611457737699617,14.656694448961845,14.701931160224072,14.747167871486303,14.792404582748532,14.837641294010758,14.882878005272987,14.928114716535214,14.973351427797443,15.01858813905967,15.063824850321899,15.109061561584125,15.154298272846354,15.199534984108581,15.244771695370808,15.290008406633037,15.335245117895264,15.380481829157493,15.42571854041972,15.470955251681948,15.516191962944175,15.561428674206406,15.606665385468634,15.651902096730861,15.69713880799309,15.742375519255317,15.787612230517546,15.832848941779773,15.878085653042001,15.923322364304228,15.968559075566455,16.013795786828684,16.05903249809091,16.104269209353138,16.14950592061537,16.194742631877595,16.239979343139822,16.28521605440205,16.33045276566428,16.375689476926507,16.420926188188737,16.466162899450964,16.51139961071319,16.55663632197542,16.60187303323765,16.647109744499875,16.692346455762102,16.73758316702433,16.78281987828656,16.828056589548787,16.873293300811014,16.91853001207324,16.96376672333547,17.009003434597698,17.054240145859925,17.099476857122152,17.144713568384383,17.18995027964661,17.235186990908836,17.280423702171067,17.325660413433294,17.37089712469552,17.41613383595775,17.46137054721998,17.506607258482205,17.551843969744432,17.597080681006663,17.64231739226889,17.687554103531117,17.732790814793344,17.778027526055574,17.8232642373178,17.868500948580028,17.913737659842255,17.958974371104482,18.004211082366712,18.04944779362894,18.09468450489117,18.139921216153397,18.185157927415624,18.230394638677854,18.27563134994008,18.320868061202308,18.366104772464535,18.411341483726765,18.456578194988992,18.50181490625122,18.547051617513446,18.592288328775677,18.637525040037904,18.68276175130013,18.727998462562358,18.773235173824585,18.818471885086815,18.863708596349042,18.90894530761127,18.9541820188735,18.999418730135726,19.044655441397957,19.089892152660184,19.13512886392241,19.180365575184638,19.22560228644687,19.270838997709095,19.316075708971322,19.36131242023355,19.406549131495776,19.451785842758007,19.497022554020234,19.54225926528246,19.587495976544687,19.632732687806918,19.677969399069145,19.723206110331372,19.768442821593602,19.81367953285583,19.85891624411806,19.904152955380287,19.949389666642514,19.99462637790474,20.03986308916697,20.085099800429198,20.130336511691425,20.175573222953652,20.22080993421588,20.26604664547811,20.311283356740336,20.356520068002563,20.40175677926479,20.44699349052702,20.492230201789248,20.537466913051475,20.5827036243137,20.627940335575932,20.673177046838163,20.71841375810039,20.763650469362616,20.808887180624843,20.85412389188707,20.8993606031493,20.944597314411528,20.989834025673755,21.03507073693598,21.080307448198212,21.12554415946044,21.170780870722666,21.216017581984893,21.261254293247124,21.30649100450935,21.351727715771577,21.396964427033804,21.44220113829603,21.487437849558265,21.532674560820492,21.57791127208272,21.623147983344946,21.668384694607173,21.713621405869404,21.75885811713163,21.804094828393858,21.849331539656085,21.894568250918315,21.939804962180542,21.98504167344277,22.030278384704996,22.075515095967223,22.120751807229453,22.16598851849168,22.211225229753907,22.256461941016134,22.301698652278365,22.346935363540595,22.392172074802822,22.43740878606505,22.482645497327276,22.527882208589507,22.573118919851733,22.61835563111396,22.663592342376187,22.708829053638418,22.754065764900645,22.79930247616287,22.8445391874251,22.889775898687326,22.935012609949556,22.980249321211783,23.02548603247401,23.070722743736237,23.115959454998467,23.161196166260698,23.206432877522925,23.251669588785152,23.29690630004738,23.34214301130961,23.387379722571836,23.432616433834063,23.47785314509629,23.523089856358517,23.568326567620748,23.613563278882975,23.6587999901452,23.70403670140743,23.74927341266966,23.794510123931886,23.839746835194113,23.88498354645634,23.93022025771857,23.9754569689808,24.020693680243028,24.065930391505255,24.11116710276748,24.156403814029712,24.20164052529194,24.246877236554166,24.292113947816393,24.33735065907862,24.38258737034085,24.427824081603077,24.473060792865304,24.51829750412753,24.563534215389762,24.60877092665199,24.654007637914216,24.699244349176443,24.74448106043867,24.7897177717009,24.83495448296313,24.880191194225358,24.925427905487584,24.97066461674981,25.015901328012042,25.06113803927427,25.106374750536496,25.151611461798723,25.196848173060953,25.24208488432318,25.287321595585407,25.332558306847634,25.377795018109865,25.42303172937209,25.46826844063432,25.513505151896545,25.558741863158772,25.603978574421003,25.64921528568323,25.69445199694546,25.739688708207687,25.784925419469914,25.830162130732145,25.87539884199437,25.9206355532566,25.965872264518826,26.011108975781056,26.056345687043283,26.10158239830551,26.146819109567737,26.192055820829964,26.237292532092194,26.28252924335442,26.32776595461665,26.373002665878875,26.418239377141106,26.463476088403333,26.508712799665563,26.55394951092779,26.599186222190017,26.644422933452248,26.689659644714474,26.7348963559767,26.78013306723893,26.82536977850116,26.870606489763386,26.915843201025613,26.96107991228784,27.006316623550067,27.051553334812297,27.096790046074524,27.14202675733675,27.187263468598978,27.23250017986121,27.277736891123435,27.322973602385662,27.36821031364789,27.41344702491012,27.458683736172347,27.503920447434574,27.5491571586968,27.594393869959028,27.639630581221258,27.684867292483485,27.730104003745712,27.775340715007946,27.820577426270173,27.8658141375324,27.91105084879463,27.956287560056857,28.001524271319084,28.04676098258131,28.091997693843542,28.13723440510577,28.182471116367996,28.227707827630223,28.272944538892453,28.31818125015468,28.363417961416907,28.408654672679134,28.45389138394136,28.49912809520359,28.54436480646582,28.589601517728045,28.634838228990272,28.680074940252503,28.72531165151473,28.770548362776957,28.815785074039184,28.861021785301414,28.90625849656364,28.951495207825868,28.996731919088095,29.041968630350322,29.087205341612552,29.13244205287478,29.177678764137006,29.222915475399233,29.268152186661464,29.31338889792369,29.358625609185918,29.403862320448145,29.44909903171038,29.494335742972606,29.539572454234836,29.584809165497063,29.63004587675929,29.675282588021517,29.720519299283747,29.765756010545974,29.8109927218082,29.85622943307043,29.901466144332655,29.946702855594886,29.991939566857113,30.03717627811934,30.082412989381567,30.127649700643797,30.172886411906024,30.21812312316825,30.263359834430478,30.30859654569271,30.353833256954935,30.399069968217162,30.44430667947939,30.489543390741616,30.534780102003847,30.580016813266074,30.6252535245283,30.670490235790528,30.715726947052758,30.760963658314985,30.806200369577212,30.85143708083944,30.896673792101666,30.941910503363896,30.987147214626123,31.03238392588835,31.077620637150577,31.12285734841281,31.16809405967504,31.21333077093727,31.258567482199496,31.303804193461723,31.34904090472395,31.39427761598618,31.439514327248407,31.484751038510634,31.52998774977286,31.57522446103509,31.62046117229732,31.665697883559545,31.710934594821772,31.756171306084003,31.80140801734623,31.846644728608457,31.891881439870684,31.93711815113291,31.98235486239514,32.02759157365737,32.072828284919595,32.11806499618182,32.16330170744405,32.208538418706276,32.25377512996851,32.29901184123074,32.344248552492964,32.38948526375519,32.43472197501742,32.479958686279645,32.52519539754187,32.5704321088041,32.615668820066325,32.66090553132856,32.706142242590786,32.75137895385301,32.79661566511524,32.841852376377474,32.8870890876397,32.93232579890193,32.977562510164155,33.02279922142638,33.06803593268861,33.11327264395084,33.15850935521307,33.2037460664753,33.248982777737524,33.29421948899975,33.33945620026198,33.384692911524205,33.42992962278643,33.47516633404866,33.52040304531089,33.56563975657312,33.61087646783535,33.656113179097574,33.7013498903598,33.74658660162203,33.791823312884254,33.83706002414648,33.88229673540871,33.92753344667094,33.97277015793317,34.018006869195396,34.06324358045762,34.10848029171985,34.15371700298208,34.198953714244304,34.24419042550653,34.289427136768765,34.33466384803099,34.37990055929322,34.425137270555446,34.47037398181767,34.51561069307991,34.560847404342134,34.60608411560436,34.65132082686659,34.696557538128815,34.74179424939104,34.787030960653276,34.8322676719155,34.87750438317773,34.92274109443996,34.96797780570218,35.01321451696441,35.05845122822664,35.103687939488864,35.1489246507511,35.194161362013325,35.23939807327555,35.28463478453778,35.329871495800006,35.37510820706223,35.42034491832446,35.46558162958669,35.510818340848914,35.55605505211115,35.601291763373375,35.6465284746356,35.69176518589783,35.737001897160056,35.78223860842228,35.82747531968451,35.87271203094674,35.917948742208964,35.9631854534712,36.008422164733425,36.05365887599565,36.09889558725788,36.144132298520105,36.18936900978234,36.23460572104457,36.27984243230679,36.32507914356902,36.37031585483125,36.41555256609348,36.46078927735571,36.506025988617935,36.55126269988016,36.59649941114239,36.641736122404616,36.68697283366684,36.73220954492907,36.7774462561913,36.82268296745353,36.86791967871576,36.913156389977985,36.95839310124021,37.00362981250244,37.048866523764666,37.09410323502689,37.13933994628912,37.184576657551354,37.22981336881358,37.27505008007581,37.320286791338035,37.36552350260026,37.41076021386249,37.455996925124715,37.50123363638694,37.54647034764917,37.5917070589114,37.63694377017363,37.68218048143586,37.727417192698084,37.77265390396031,37.81789061522254,37.86312732648477,37.908364037747,37.953600749009226,37.99883746027145,38.04407417153369,38.089310882795914,38.13454759405814,38.17978430532037,38.225021016582595,38.27025772784482,38.31549443910705,38.360731150369276,38.4059678616315,38.45120457289374,38.496441284155964,38.54167799541819,38.58691470668042,38.632151417942644,38.67738812920487,38.7226248404671,38.767861551729325,38.81309826299155,38.858334974253786,38.90357168551601,38.94880839677824,38.99404510804047,39.039281819302694,39.08451853056492,39.12975524182715,39.174991953089375,39.2202286643516,39.265465375613836,39.31070208687606,39.35593879813829,39.40117550940052,39.446412220662744,39.49164893192497,39.536885643187205,39.58212235444943,39.62735906571166,39.672595776973886,39.71783248823612,39.76306919949835,39.80830591076057,39.8535426220228,39.89877933328503,39.944016044547254,39.98925275580948,40.03448946707171,40.07972617833394,40.12496288959617,40.170199600858396,40.21543631212062,40.26067302338285,40.30590973464508,40.351146445907304,40.39638315716953,40.44161986843176,40.48685657969399,40.53209329095622,40.577330002218446,40.62256671348067,40.6678034247429,40.71304013600513,40.758276847267354,40.80351355852958,40.84875026979181,40.89398698105404,40.93922369231627,40.984460403578495,41.02969711484072,41.07493382610295,41.120170537365176,41.1654072486274,41.21064395988963,41.255880671151864,41.30111738241409,41.346354093676325,41.39159080493855,41.43682751620078,41.482064227463006,41.52730093872523,41.57253764998746,41.61777436124969,41.663011072511914,41.70824778377414,41.753484495036375,41.7987212062986,41.84395791756083,41.889194628823056,41.93443134008528,41.97966805134751,42.02490476260974,42.07014147387196,42.11537818513419,42.160614896396424,42.20585160765865,42.25108831892088,42.296325030183105,42.34156174144533,42.38679845270756,42.432035163969786,42.47727187523201,42.52250858649425,42.567745297756474,42.6129820090187,42.65821872028093,42.703455431543155,42.74869214280538,42.79392885406761,42.839165565329836,42.88440227659206,42.9296389878543,42.97487569911653,43.02011241037876,43.065349121640985,43.11058583290321,43.15582254416544,43.201059255427666,43.24629596668989,43.29153267795212,43.336769389214346,43.38200610047658,43.42724281173881,43.472479523001034,43.51771623426326,43.56295294552549,43.608189656787715,43.65342636804994,43.69866307931217,43.743899790574396,43.78913650183663,43.83437321309886,43.879609924361084,43.92484663562331,43.97008334688554,44.015320058147765,44.06055676940999,44.10579348067222,44.151030191934446,44.19626690319668,44.24150361445891,44.286740325721134,44.33197703698336,44.37721374824559,44.422450459507814,44.46768717077004,44.51292388203227,44.5581605932945,44.60339730455673,44.64863401581896,44.69387072708119,44.73910743834342,44.784344149605644,44.82958086086787,44.8748175721301,44.920054283392325,44.96529099465455,45.01052770591678,45.05576441717901,45.10100112844124,45.14623783970347,45.191474550965694,45.23671126222792,45.28194797349015,45.327184684752375,45.3724213960146,45.417658107276836,45.46289481853906,45.50813152980129,45.55336824106352,45.59860495232574,45.64384166358797,45.6890783748502,45.734315086112424,45.77955179737465,45.824788508636885,45.87002521989911,45.91526193116134,45.960498642423566,46.00573535368579,46.05097206494802,46.09620877621025,46.141445487472474,46.1866821987347,46.231918909996935,46.27715562125917,46.322392332521396,46.36762904378362,46.41286575504585,46.45810246630808,46.503339177570304,46.54857588883253,46.59381260009476,46.639049311356985,46.68428602261922,46.729522733881446,46.77475944514367,46.8199961564059,46.86523286766813,46.91046957893035,46.95570629019258,47.00094300145481,47.046179712717034,47.09141642397927,47.136653135241495,47.18188984650372,47.22712655776595,47.272363269028176,47.3175999802904,47.36283669155263,47.40807340281486,47.45331011407709,47.49854682533932,47.543783536601545,47.58902024786377,47.634256959126,47.679493670388226,47.72473038165045,47.76996709291268,47.81520380417491,47.86044051543714,47.90567722669937,47.9509139379616,47.99615064922383,48.041387360486056,48.08662407174828,48.13186078301051,48.177097494272736,48.22233420553496,48.26757091679719,48.312807628059424,48.35804433932165,48.40328105058388,48.448517761846105,48.49375447310833,48.53899118437056,48.584227895632786,48.62946460689501,48.67470131815724,48.719938029419474,48.7651747406817,48.81041145194393,48.855648163206155,48.90088487446838,48.94612158573061,48.991358296992836,49.03659500825506,49.08183171951729,49.127068430779524,49.17230514204175,49.21754185330398,49.262778564566204,49.30801527582843,49.35325198709066,49.398488698352885,49.44372540961511,49.48896212087734,49.53419883213957,49.5794355434018,49.62467225466403,49.66990896592626,49.71514567718849,49.760382388450715,49.80561909971294,49.85085581097517,49.896092522237396,49.94132923349962,49.98656594476186,50.031802656024084,50.07703936728631,50.12227607854854,50.167512789810765,50.21274950107299,50.25798621233522,50.303222923597446,50.34845963485968,50.39369634612191,50.43893305738413,50.48416976864636,50.52940647990859,50.574643191170814,50.61987990243304,50.66511661369527,50.710353324957495,50.75559003621973,50.800826747481956,50.84606345874418,50.89130017000641,50.93653688126864,50.981773592530864,51.02701030379309,51.07224701505532,51.117483726317545,51.16272043757978,51.207957148842006,51.25319386010423,51.29843057136646,51.343667282628694,51.38890399389092,51.43414070515315,51.479377416415375,51.5246141276776,51.56985083893983,51.61508755020206,51.66032426146429,51.705560972726516,51.75079768398874,51.79603439525097,51.8412711065132,51.886507817775424,51.93174452903765,51.97698124029988,52.02221795156211,52.06745466282434,52.112691374086566,52.15792808534879,52.20316479661102,52.24840150787325,52.293638219135474,52.3388749303977,52.38411164165993,52.42934835292216,52.47458506418439,52.519821775446616,52.56505848670884,52.61029519797107,52.6555319092333,52.70076862049552,52.74600533175775,52.791242043019984,52.83647875428221,52.88171546554444,52.926952176806665,52.97218888806889,53.017425599331126,53.06266231059335,53.10789902185558,53.15313573311781,53.198372444380034,53.24360915564227,53.288845866904495,53.33408257816672,53.37931928942895,53.424556000691176,53.4697927119534,53.51502942321563,53.56026613447786,53.605502845740084,53.65073955700232,53.695976268264545,53.74121297952677,53.786449690789,53.831686402051226],"type":"scatter"},{"line":{"color":"#3498db","width":2},"mode":"lines","name":"Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926],"y":[0,0.043011902852694406,0.08602380570538881,0.12903570855808322,0.17204761141077762,0.215059514263472,0.25807141711616643,0.30108331996886084,0.34409522282155525,0.38710712567424965,0.430119028526944,0.47313093137963846,0.5161428342323329,0.5591547370850272,0.6021666399377217,0.6451785427904161,0.6881904456431105,0.7312023484958049,0.7742142513484993,0.8172261542011936,0.860238057053888,0.9032499599065825,0.9462618627592769,0.9892737656119713,1.0322856684646657,1.07529757131736,1.1183094741700543,1.1613213770227488,1.2043332798754434,1.2473451827281377,1.2903570855808322,1.3333689884335267,1.376380891286221,1.4193927941389153,1.4624046969916098,1.505416599844304,1.5484285026969986,1.591440405549693,1.6344523084023872,1.6774642112550817,1.720476114107776,1.7634880169604703,1.806499919813165,1.8495118226658593,1.8925237255185539,1.9355356283712482,1.9785475312239427,2.0215594340766367,2.0645713369293315,2.1075832397820258,2.15059514263472,2.1936070454874144,2.2366189483401087,2.2796308511928034,2.3226427540454977,2.3656546568981924,2.4086665597508867,2.451678462603581,2.4946903654562753,2.5377022683089696,2.5807141711616643,2.6237260740143586,2.6667379768670534,2.7097498797197472,2.752761782572442,2.7957736854251363,2.8387855882778306,2.881797491130525,2.9248093939832196,2.9678212968359134,3.010833199688608,3.0538451025413025,3.096857005393997,3.1398689082466915,3.182880811099386,3.2258927139520805,3.2689046168047744,3.311916519657469,3.3549284225101634,3.3979403253628577,3.440952228215552,3.4839641310682468,3.5269760339209406,3.5699879367736354,3.61299983962633,3.6560117424790244,3.6990236453317187,3.742035548184413,3.7850474510371077,3.8280593538898016,3.8710712567424963,3.9140831595951906,3.9570950624478853,4.00010696530058,4.0431188681532735,4.086130771005968,4.129142673858663,4.172154576711358,4.2151664795640515,4.258178382416746,4.30119028526944,4.344202188122135,4.387214090974829,4.4302259938275235,4.473237896680217,4.516249799532912,4.559261702385607,4.6022736052383015,4.645285508090995,4.68829741094369,4.731309313796385,4.774321216649079,4.8173331195017735,4.860345022354467,4.903356925207162,4.946368828059856,4.989380730912551,5.032392633765245,5.075404536617939,5.118416439470634,5.161428342323329,5.2044402451760226,5.247452148028717,5.290464050881412,5.333475953734107,5.3764878565868,5.4194997594394945,5.462511662292189,5.505523565144884,5.548535467997578,5.5915473708502725,5.634559273702966,5.677571176555661,5.720583079408356,5.76359498226105,5.8066068851137445,5.849618787966439,5.892630690819134,5.935642593671827,5.978654496524522,6.021666399377216,6.064678302229911,6.107690205082605,6.1507021079353,6.193714010787994,6.236725913640688,6.279737816493383,6.322749719346077,6.365761622198772,6.408773525051466,6.451785427904161,6.494797330756854,6.537809233609549,6.5808211364622435,6.623833039314938,6.666844942167633,6.709856845020327,6.752868747873022,6.7958806507257155,6.83889255357841,6.881904456431104,6.924916359283799,6.9679282621364935,7.010940164989188,7.053952067841881,7.096963970694576,7.139975873547271,7.182987776399965,7.22599967925266,7.269011582105354,7.312023484958049,7.3550353878107435,7.398047290663437,7.441059193516131,7.484071096368826,7.527082999221521,7.570094902074215,7.613106804926909,7.656118707779603,7.699130610632298,7.742142513484993,7.785154416337687,7.828166319190381,7.871178222043076,7.914190124895771,7.957202027748465,8.00021393060116,8.043225833453853,8.086237736306547,8.129249639159243,8.172261542011936,8.21527344486463,8.258285347717326,8.30129725057002,8.344309153422715,8.387321056275407,8.430332959128103,8.473344861980797,8.516356764833493,8.559368667686186,8.60238057053888,8.645392473391576,8.68840437624427,8.731416279096964,8.774428181949657,8.817440084802353,8.860451987655047,8.903463890507743,8.946475793360435,8.98948769621313,9.032499599065824,9.07551150191852,9.118523404771214,9.161535307623907,9.204547210476603,9.247559113329297,9.29057101618199,9.333582919034685,9.37659482188738,9.419606724740074,9.46261862759277,9.505630530445462,9.548642433298157,9.591654336150851,9.634666239003547,9.67767814185624,9.720690044708935,9.76370194756163,9.806713850414324,9.849725753267018,9.892737656119712,9.935749558972407,9.978761461825101,10.021773364677795,10.06478526753049,10.107797170383185,10.150809073235878,10.193820976088574,10.236832878941268,10.279844781793964,10.322856684646657,10.36586858749935,10.408880490352045,10.451892393204739,10.494904296057435,10.537916198910128,10.580928101762824,10.623940004615518,10.666951907468214,10.709963810320906,10.7529757131736,10.795987616026295,10.838999518878989,10.882011421731685,10.925023324584378,10.968035227437074,11.011047130289768,11.054059033142462,11.097070935995156,11.14008283884785,11.183094741700545,11.226106644553239,11.269118547405933,11.312130450258628,11.355142353111322,11.398154255964018,11.441166158816712,11.484178061669404,11.5271899645221,11.570201867374793,11.613213770227489,11.656225673080183,11.699237575932878,11.742249478785572,11.785261381638268,11.828273284490962,11.871285187343654,11.91429709019635,11.957308993049043,12.000320895901739,12.043332798754433,12.086344701607128,12.129356604459822,12.172368507312518,12.21538041016521,12.258392313017904,12.3014042158706,12.344416118723293,12.387428021575989,12.430439924428683,12.473451827281377,12.516463730134072,12.559475632986766,12.60248753583946,12.645499438692154,12.688511341544848,12.731523244397543,12.774535147250237,12.817547050102933,12.860558952955627,12.903570855808322,12.946582758661016,12.989594661513708,13.032606564366404,13.075618467219098,13.118630370071793,13.161642272924487,13.204654175777183,13.247666078629877,13.290677981482572,13.333689884335266,13.376701787187958,13.419713690040654,13.462725592893348,13.505737495746043,13.548749398598737,13.591761301451431,13.634773204304127,13.67778510715682,13.720797010009514,13.763808912862208,13.806820815714904,13.849832718567598,13.892844621420291,13.935856524272987,13.978868427125681,14.021880329978377,14.06489223283107,14.107904135683762,14.150916038536458,14.193927941389152,14.236939844241848,14.279951747094541,14.322963649947237,14.36597555279993,14.408987455652627,14.45199935850532,14.495011261358012,14.538023164210708,14.581035067063402,14.624046969916098,14.667058872768791,14.710070775621487,14.75308267847418,14.796094581326875,14.83910648417957,14.882118387032262,14.925130289884958,14.968142192737652,15.011154095590346,15.054165998443041,15.097177901295735,15.14018980414843,15.183201707001125,15.226213609853819,15.269225512706512,15.312237415559206,15.355249318411902,15.398261221264596,15.441273124117291,15.484285026969985,15.52729692982268,15.570308832675375,15.613320735528067,15.656332638380762,15.699344541233456,15.742356444086152,15.785368346938846,15.828380249791541,15.871392152644235,15.91440405549693,15.957415958349625,16.00042786120232,16.043439764055012,16.086451666907706,16.1294635697604,16.172475472613094,16.21548737546579,16.258499278318485,16.30151118117118,16.344523084023873,16.387534986876567,16.43054688972926,16.473558792581954,16.516570695434652,16.559582598287346,16.60259450114004,16.645606403992733,16.68861830684543,16.73163020969812,16.774642112550815,16.817654015403512,16.860665918256206,16.9036778211089,16.946689723961594,16.98970162681429,17.032713529666985,17.07572543251968,17.118737335372373,17.161749238225067,17.20476114107776,17.247773043930454,17.290784946783152,17.333796849635846,17.37680875248854,17.419820655341233,17.462832558193927,17.50584446104662,17.548856363899315,17.59186826675201,17.634880169604706,17.6778920724574,17.720903975310094,17.763915878162788,17.806927781015485,17.849939683868175,17.89295158672087,17.935963489573567,17.97897539242626,18.021987295278954,18.06499919813165,18.108011100984346,18.15102300383704,18.194034906689733,18.237046809542427,18.28005871239512,18.323070615247815,18.36608251810051,18.409094420953206,18.4521063238059,18.495118226658594,18.538130129511288,18.58114203236398,18.624153935216675,18.66716583806937,18.710177740922067,18.75318964377476,18.796201546627454,18.839213449480148,18.882225352332842,18.92523725518554,18.968249158038233,19.011261060890924,19.05427296374362,19.097284866596315,19.14029676944901,19.183308672301703,19.2263205751544,19.269332478007094,19.312344380859788,19.35535628371248,19.398368186565175,19.44138008941787,19.484391992270563,19.52740389512326,19.570415797975954,19.613427700828648,19.656439603681342,19.699451506534036,19.74246340938673,19.785475312239424,19.82848721509212,19.871499117944815,19.91451102079751,19.957522923650203,20.000534826502896,20.04354672935559,20.086558632208288,20.12957053506098,20.172582437913675,20.21559434076637,20.258606243619067,20.301618146471757,20.34463004932445,20.387641952177148,20.43065385502984,20.473665757882536,20.51667766073523,20.559689563587927,20.602701466440617,20.645713369293315,20.68872527214601,20.7317371749987,20.774749077851396,20.81776098070409,20.860772883556788,20.903784786409478,20.946796689262175,20.98980859211487,21.032820494967567,21.075832397820257,21.11884430067295,21.161856203525648,21.20486810637834,21.247880009231036,21.29089191208373,21.333903814936427,21.376915717789117,21.41992762064181,21.46293952349451,21.5059514263472,21.548963329199896,21.59197523205259,21.634987134905288,21.677999037757978,21.721010940610675,21.76402284346337,21.80703474631606,21.850046649168757,21.89305855202145,21.936070454874148,21.97908235772684,22.022094260579536,22.06510616343223,22.108118066284923,22.151129969137617,22.19414187199031,22.23715377484301,22.2801656776957,22.323177580548396,22.36618948340109,22.409201386253784,22.452213289106478,22.495225191959175,22.538237094811866,22.58124899766456,22.624260900517257,22.66727280336995,22.710284706222645,22.75329660907534,22.796308511928036,22.839320414780726,22.882332317633423,22.925344220486117,22.968356123338808,23.011368026191505,23.0543799290442,23.097391831896896,23.140403734749587,23.183415637602284,23.226427540454978,23.269439443307675,23.312451346160366,23.35546324901306,23.398475151865757,23.441487054718447,23.484498957571144,23.52751086042384,23.570522763276536,23.613534666129226,23.656546568981923,23.699558471834617,23.742570374687308,23.785582277540005,23.8285941803927,23.871606083245396,23.914617986098087,23.957629888950784,24.000641791803478,24.043653694656175,24.086665597508865,24.12967750036156,24.172689403214257,24.215701306066947,24.258713208919644,24.30172511177234,24.344737014625036,24.387748917477726,24.43076082033042,24.473772723183117,24.516784626035808,24.559796528888505,24.6028084317412,24.645820334593893,24.688832237446587,24.731844140299284,24.774856043151978,24.817867946004668,24.860879848857365,24.90389175171006,24.946903654562753,24.989915557415447,25.032927460268144,25.075939363120835,25.118951265973532,25.161963168826226,25.20497507167892,25.247986974531614,25.290998877384308,25.334010780237005,25.377022683089695,25.420034585942393,25.463046488795086,25.506058391647784,25.549070294500474,25.592082197353168,25.635094100205865,25.678106003058556,25.721117905911253,25.764129808763947,25.807141711616644,25.850153614469335,25.893165517322032,25.936177420174726,25.979189323027416,26.022201225880114,26.065213128732807,26.108225031585505,26.151236934438195,26.194248837290893,26.237260740143586,26.280272642996284,26.323284545848974,26.366296448701668,26.409308351554365,26.452320254407056,26.495332157259753,26.538344060112447,26.581355962965144,26.624367865817835,26.667379768670532,26.710391671523226,26.753403574375916,26.796415477228614,26.839427380081307,26.882439282934005,26.925451185786695,26.968463088639393,27.011474991492086,27.054486894344777,27.097498797197474,27.140510700050168,27.183522602902862,27.226534505755556,27.269546408608253,27.312558311460947,27.35557021431364,27.398582117166335,27.44159402001903,27.484605922871722,27.527617825724416,27.570629728577114,27.613641631429807,27.6566535342825,27.699665437135195,27.742677339987893,27.785689242840583,27.828701145693277,27.871713048545974,27.914724951398664,27.957736854251362,28.000748757104056,28.043760659956753,28.086772562809443,28.12978446566214,28.172796368514835,28.215808271367525,28.258820174220222,28.301832077072916,28.344843979925614,28.387855882778304,28.430867785631,28.473879688483695,28.516891591336393,28.559903494189083,28.602915397041777,28.645927299894474,28.688939202747164,28.73195110559986,28.774963008452556,28.817974911305253,28.860986814157943,28.90399871701064,28.947010619863335,28.990022522716025,29.033034425568722,29.076046328421416,29.119058231274114,29.162070134126804,29.2050820369795,29.248093939832195,29.29110584268489,29.334117745537583,29.377129648390277,29.420141551242974,29.463153454095664,29.50616535694836,29.549177259801056,29.59218916265375,29.635201065506443,29.67821296835914,29.721224871211835,29.764236774064525,29.807248676917222,29.850260579769916,29.89327248262261,29.936284385475304,29.979296288328,30.02230819118069,30.065320094033385,30.108331996886083,30.151343899738777,30.19435580259147,30.237367705444164,30.28037960829686,30.323391511149552,30.36640341400225,30.409415316854943,30.452427219707637,30.49543912256033,30.538451025413025,30.581462928265722,30.624474831118413,30.66748673397111,30.710498636823804,30.7535105396765,30.79652244252919,30.839534345381885,30.882546248234583,30.925558151087273,30.96857005393997,31.011581956792664,31.05459385964536,31.097605762498052,31.14061766535075,31.183629568203443,31.226641471056134,31.26965337390883,31.312665276761525,31.355677179614222,31.398689082466912,31.44170098531961,31.484712888172304,31.527724791025,31.57073669387769,31.613748596730385,31.656760499583083,31.699772402435773,31.74278430528847,31.785796208141164,31.82880811099386,31.871820013846552,31.91483191669925,31.957843819551943,32.00085572240464,32.043867625257334,32.086879528110025,32.12989143096272,32.17290333381541,32.21591523666811,32.2589271395208,32.3019390423735,32.34495094522619,32.387962848078885,32.43097475093158,32.47398665378427,32.51699855663697,32.56001045948966,32.60302236234236,32.64603426519505,32.689046168047746,32.73205807090044,32.77506997375313,32.81808187660583,32.86109377945852,32.90410568231122,32.94711758516391,32.990129488016606,33.033141390869304,33.076153293721994,33.11916519657469,33.16217709942738,33.20518900228008,33.24820090513277,33.29121280798547,33.334224710838164,33.37723661369086,33.42024851654355,33.46326041939624,33.50627232224894,33.54928422510163,33.59229612795433,33.635308030807025,33.67831993365972,33.72133183651241,33.76434373936511,33.8073556422178,33.85036754507049,33.89337944792319,33.936391350775885,33.97940325362858,34.02241515648127,34.06542705933397,34.10843896218666,34.15145086503936,34.19446276789205,34.237474670744746,34.28048657359744,34.32349847645013,34.36651037930283,34.40952228215552,34.45253418500822,34.49554608786091,34.538557990713606,34.581569893566304,34.624581796418994,34.66759369927169,34.71060560212438,34.75361750497708,34.79662940782977,34.83964131068247,34.882653213535164,34.925665116387854,34.96867701924055,35.01168892209324,35.05470082494594,35.09771272779863,35.14072463065133,35.18373653350402,35.226748436356715,35.26976033920941,35.31277224206211,35.3557841449148,35.39879604776749,35.44180795062019,35.48481985347288,35.527831756325575,35.57084365917827,35.61385556203097,35.65686746488366,35.69987936773635,35.74289127058905,35.78590317344174,35.828915076294436,35.87192697914713,35.91493888199983,35.95795078485252,36.00096268770522,36.04397459055791,36.0869864934106,36.1299983962633,36.173010299115994,36.21602220196869,36.25903410482138,36.30204600767408,36.34505791052677,36.38806981337947,36.43108171623216,36.474093619084854,36.51710552193755,36.56011742479024,36.60312932764294,36.64614123049563,36.68915313334833,36.73216503620102,36.775176939053715,36.81818884190641,36.8612007447591,36.9042126476118,36.94722455046449,36.99023645331719,37.03324835616988,37.076260259022575,37.11927216187527,37.16228406472796,37.20529596758066,37.24830787043335,37.29131977328605,37.33433167613874,37.377343578991436,37.42035548184413,37.463367384696824,37.50637928754952,37.54939119040222,37.59240309325491,37.6354149961076,37.678426898960296,37.721438801812994,37.764450704665684,37.80746260751838,37.85047451037108,37.89348641322377,37.93649831607647,37.97951021892916,38.02252212178185,38.065534024634545,38.10854592748724,38.15155783033994,38.19456973319263,38.23758163604533,38.28059353889802,38.323605441750715,38.366617344603405,38.4096292474561,38.4526411503088,38.49565305316149,38.53866495601419,38.58167685886688,38.624688761719575,38.667700664572266,38.71071256742496,38.75372447027766,38.79673637313035,38.83974827598305,38.88276017883574,38.925772081688436,38.968783984541126,39.01179588739382,39.05480779024652,39.09781969309921,39.14083159595191,39.1838434988046,39.226855401657296,39.26986730450999,39.312879207362684,39.35589111021538,39.39890301306807,39.44191491592077,39.48492681877346,39.52793872162616,39.57095062447885,39.613962527331545,39.65697443018424,39.69998633303693,39.74299823588963,39.78601013874233,39.82902204159502],"type":"scatter"},{"line":{"color":"#2ecc71","width":2},"mode":"lines","name":"Rule 6","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156],"y":[0,0.14432319873142468,0.28864639746284937,0.4329695961942741,0.5772927949256987,0.7216159936571234,0.8659391923885482,1.0102623911199728,1.1545855898513975,1.2989087885828223,1.4432319873142467,1.5875551860456716,1.7318783847770964,1.8762015835085208,2.0205247822399457,2.1648479809713703,2.309171179702795,2.4534943784342196,2.5978175771656447,2.742140775897069,2.8864639746284935,3.030787173359918,3.175110372091343,3.319433570822768,3.463756769554193,3.6080799682856175,3.7524031670170417,3.8967263657484663,4.041049564479891,4.1853727632113165,4.329695961942741,4.474019160674165,4.61834235940559,4.762665558137015,4.906988756868439,5.051311955599864,5.195635154331289,5.3399583530627135,5.484281551794138,5.628604750525563,5.772927949256987,5.917251147988412,6.061574346719836,6.205897545451262,6.350220744182686,6.4945439429141105,6.638867141645536,6.78319034037696,6.927513539108386,7.071836737839809,7.216159936571235,7.360483135302659,7.504806334034083,7.649129532765508,7.793452731496933,7.937775930228359,8.082099128959783,8.226422327691207,8.370745526422633,8.515068725154055,8.659391923885481,8.803715122616905,8.94803832134833,9.092361520079756,9.23668471881118,9.381007917542606,9.52533111627403,9.669654315005454,9.813977513736878,9.958300712468303,10.102623911199728,10.246947109931153,10.391270308662579,10.535593507394003,10.679916706125427,10.824239904856851,10.968563103588275,11.112886302319701,11.257209501051125,11.40153269978255,11.545855898513974,11.6901790972454,11.834502295976824,11.978825494708248,12.123148693439672,12.267471892171097,12.411795090902524,12.556118289633948,12.700441488365373,12.844764687096797,12.989087885828221,13.133411084559647,13.277734283291071,13.422057482022495,13.56638068075392,13.710703879485344,13.855027078216771,13.999350276948196,14.143673475679618,14.287996674411042,14.43231987314247,14.576643071873894,14.720966270605318,14.865289469336743,15.009612668068167,15.153935866799593,15.298259065531017,15.442582264262441,15.586905462993865,15.73122866172529,15.875551860456717,16.01987505918814,16.164198257919566,16.30852145665099,16.452844655382414,16.59716785411384,16.741491052845266,16.88581425157669,17.03013745030811,17.174460649039535,17.318783847770963,17.463107046502387,17.60743024523381,17.751753443965235,17.89607664269666,18.040399841428087,18.18472304015951,18.329046238890935,18.47336943762236,18.617692636353784,18.76201583508521,18.906339033816636,19.05066223254806,19.194985431279484,19.33930863001091,19.483631828742332,19.627955027473757,19.77227822620518,19.916601424936605,20.060924623668033,20.205247822399457,20.34957102113088,20.493894219862305,20.63821741859373,20.782540617325157,20.92686381605658,21.071187014788006,21.21551021351943,21.359833412250854,21.504156610982278,21.648479809713702,21.792803008445127,21.93712620717655,22.081449405907975,22.225772604639403,22.370095803370827,22.51441900210225],"type":"scatter"},{"line":{"color":"#f39c12","width":2},"mode":"lines","name":"Prof Rule 7","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156],"y":[0,0.14588784921679185,0.2917756984335837,0.4376635476503756,0.5835513968671674,0.7294392460839592,0.8753270953007511,1.021214944517543,1.1671027937343348,1.3129906429511267,1.4588784921679183,1.6047663413847104,1.7506541906015023,1.896542039818294,2.042429889035086,2.188317738251878,2.3342055874686696,2.4800934366854612,2.6259812859022533,2.771869135119045,2.9177569843358366,3.0636448335526287,3.209532682769421,3.3554205319862125,3.5013083812030046,3.6471962304197967,3.793084079636588,3.93897192885338,4.084859778070172,4.230747627286964,4.376635476503756,4.522523325720547,4.668411174937339,4.814299024154131,4.9601868733709225,5.1060747225877146,5.251962571804507,5.397850421021298,5.54373827023809,5.689626119454882,5.835513968671673,5.981401817888466,6.1272896671052575,6.27317751632205,6.419065365538842,6.564953214755633,6.710841063972425,6.856728913189216,7.002616762406009,7.1485046116228,7.294392460839593,7.440280310056385,7.586168159273176,7.732056008489968,7.87794385770676,8.023831706923552,8.169719556140343,8.315607405357134,8.461495254573927,8.607383103790719,8.753270953007512,8.899158802224303,9.045046651441094,9.190934500657887,9.336822349874678,9.482710199091471,9.628598048308262,9.774485897525054,9.920373746741845,10.066261595958638,10.212149445175429,10.35803729439222,10.503925143609013,10.649812992825805,10.795700842042596,10.941588691259389,11.08747654047618,11.233364389692973,11.379252238909764,11.525140088126555,11.671027937343347,11.816915786560141,11.962803635776933,12.108691484993724,12.254579334210515,12.400467183427306,12.5463550326441,12.69224288186089,12.838130731077683,12.984018580294475,13.129906429511266,13.275794278728059,13.42168212794485,13.567569977161641,13.713457826378432,13.859345675595224,14.005233524812018,14.15112137402881,14.2970092232456,14.442897072462392,14.588784921679187,14.734672770895978,14.88056062011277,15.02644846932956,15.172336318546352,15.318224167763145,15.464112016979936,15.609999866196727,15.75588771541352,15.901775564630311,16.047663413847104,16.193551263063895,16.339439112280687,16.485326961497478,16.63121481071427,16.777102659931064,16.922990509147855,17.068878358364646,17.214766207581437,17.36065405679823,17.506541906015023,17.652429755231815,17.798317604448606,17.944205453665397,18.090093302882188,18.235981152098983,18.381869001315774,18.527756850532565,18.673644699749357,18.819532548966148,18.965420398182943,19.111308247399734,19.257196096616525,19.403083945833316,19.548971795050107,19.6948596442669,19.84074749348369,19.986635342700485,20.132523191917276,20.278411041134067,20.424298890350858,20.57018673956765,20.71607458878444,20.861962438001232,21.007850287218027,21.153738136434818,21.29962598565161,21.4455138348684,21.59140168408519,21.737289533301986,21.883177382518777,22.02906523173557,22.17495308095236,22.32084093016915,22.466728779385946,22.612616628602737,22.75850447781953],"type":"scatter"},{"line":{"color":"#9b59b6","width":2},"mode":"lines","name":"Volume Rule 5","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69],"y":[0,0.08867244113407598,0.17734488226815195,0.26601732340222795,0.3546897645363039,0.4433622056703799,0.5320346468044559,0.6207070879385319,0.7093795290726078,0.7980519702066837,0.8867244113407599,0.9753968524748357,1.0640692936089118,1.1527417347429878,1.2414141758770638,1.3300866170111396,1.4187590581452156,1.5074314992792917,1.5961039404133675,1.6847763815474435,1.7734488226815197,1.8621212638155957,1.9507937049496713,2.0394661460837473,2.1281385872178236,2.2168110283518994,2.3054834694859756,2.3941559106200514,2.4828283517541276,2.571500792888203,2.6601732340222792,2.7488456751563555,2.8375181162904313,2.9261905574245075,3.0148629985585833,3.1035354396926595,3.192207880826735,3.2808803219608116,3.369552763094887,3.4582252042289627,3.5468976453630394,3.6355700864971148,3.7242425276311915,3.812914968765267,3.9015874098993426,3.990259851033419,4.078932292167495,4.167604733301571,4.256277174435647,4.344949615569723,4.433622056703799,4.5222944978378745,4.610966938971951,4.699639380106027,4.788311821240103,4.876984262374179,4.965656703508255,5.054329144642331,5.143001585776406,5.231674026910483,5.3203464680445585,5.409018909178635,5.497691350312711,5.586363791446786,5.6750362325808625,5.763708673714938,5.852381114849015,5.94105355598309,6.029725997117167,6.118398438251242],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"text":"Equity Curves - Top 5 Rules (Simplified)"},"xaxis":{"title":{"text":"Trade Number"}},"yaxis":{"title":{"text":"Cumulative Return (%)"}},"height":500,"hovermode":"x unified"},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html></div>
    </div>
    
    
    
    <div class="chart-container">
        <div class="chart-title">📋 Detailed Performance Table</div>
        
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Rule Name</th>
                    <th>Category</th>
                    <th>Total Return</th>
                    <th>Win Rate</th>
                    <th>Trades</th>
                    <th>Profit Factor</th>
                    <th>Sharpe Ratio</th>
                    <th>Max Drawdown</th>
                    <th>Rank Score</th>
                </tr>
            </thead>
            <tbody>
                
            <tr>
                <td>1</td>
                <td>AI Rule 10: Composite Sentiment Reversal</td>
                <td>AI_GENERATED</td>
                <td class="positive">53.83%</td>
                <td>63.4%</td>
                <td>1190</td>
                <td>1.08</td>
                <td>0.00</td>
                <td>32.20%</td>
                <td>70.6</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>Rule 7: Bollinger Band Bounce</td>
                <td>ORIGINAL</td>
                <td class="positive">39.83%</td>
                <td>63.2%</td>
                <td>926</td>
                <td>1.07</td>
                <td>0.00</td>
                <td>40.62%</td>
                <td>64.9</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>Rule 6: Stochastic Oversold Cross</td>
                <td>ORIGINAL</td>
                <td class="positive">22.51%</td>
                <td>69.2%</td>
                <td>156</td>
                <td>1.28</td>
                <td>0.00</td>
                <td>9.73%</td>
                <td>59.8</td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                <td>PROFESSIONAL</td>
                <td class="positive">22.76%</td>
                <td>68.6%</td>
                <td>156</td>
                <td>1.27</td>
                <td>0.00</td>
                <td>12.13%</td>
                <td>59.7</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>Volume Rule 5: Smart Money Volume</td>
                <td>UNKNOWN</td>
                <td class="positive">6.12%</td>
                <td>65.2%</td>
                <td>69</td>
                <td>1.15</td>
                <td>0.00</td>
                <td>6.52%</td>
                <td>42.7</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>Professional Rule 10: CCI Reversal Enhanced</td>
                <td>UNKNOWN</td>
                <td class="positive">7.91%</td>
                <td>65.6%</td>
                <td>61</td>
                <td>1.23</td>
                <td>0.00</td>
                <td>6.72%</td>
                <td>41.1</td>
            </tr>
            
            <tr>
                <td>7</td>
                <td>Price Action Rule 3: Engulfing Pattern</td>
                <td>UNKNOWN</td>
                <td class="positive">10.35%</td>
                <td>80.0%</td>
                <td>15</td>
                <td>3.45</td>
                <td>0.00</td>
                <td>2.30%</td>
                <td>32.6</td>
            </tr>
            
            <tr>
                <td>8</td>
                <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                <td>UNKNOWN</td>
                <td class="positive">2.30%</td>
                <td>63.0%</td>
                <td>27</td>
                <td>1.15</td>
                <td>0.00</td>
                <td>6.06%</td>
                <td>27.9</td>
            </tr>
            
            </tbody>
        </table>
        
    </div>
    
    <div class="chart-container">
        <div class="chart-title">⚙️ Configuration Used</div>
        <div style="background-color: #ecf0f1; padding: 15px; border-radius: 5px;">
            <strong>Risk Management:</strong><br>
            • Stop Loss: 1.3%<br>
            • Take Profit: 0.75%<br>
            • Risk/Reward Ratio: 1:0.6<br>
            • Max Holding Period: None minutes<br><br>
            
            <strong>Dataset:</strong><br>
            • Total Candles: 813,301<br>
            • Backtest Range: 300 to 813,601<br>
            • Initial Capital: $100,000
        </div>
    </div>
    
</body>
</html>
