
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Rule Effectiveness Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #2E86AB;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2E86AB;
            border-bottom: 2px solid #2E86AB;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #2E86AB;
            color: white;
            font-weight: bold;
            cursor: pointer;
            user-select: none;
            position: relative;
            transition: background-color 0.3s ease;
        }
        th:hover {
            background-color: #1E5F7A;
        }
        .sortable-table th {
            padding-right: 20px;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .positive {
            color: #27AE60;
            font-weight: bold;
        }
        .negative {
            color: #E74C3C;
            font-weight: bold;
        }
        .neutral {
            color: #7F8C8D;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trading Rule Effectiveness Dashboard</h1>
            <p>Generated on 2025-07-02 19:05:04 | Enhanced Strategy Analysis</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">131.80%</div>
                <div class="metric-label">Total Return</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">7,502</div>
                <div class="metric-label">Total Trades</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">62.4%</div>
                <div class="metric-label">Win Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">2.50</div>
                <div class="metric-label">Profit Factor</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00%</div>
                <div class="metric-label">Max Drawdown</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.00</div>
                <div class="metric-label">Sharpe Ratio</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📈 Buy Rules Performance - Dual Ranking Analysis</h2>
            <div class="chart-container">
                <div id="buyRulesChart" style="height: 500px;"></div>
            </div>

            <!-- Dual Ranking Tables -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div>
                    <h3 style="color: #2E86AB; text-align: center;">🏆 Ranked by Total Return</h3>
                    <div class="table-container">
                        
        <table id="returnTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('returnTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('returnTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('returnTable', 2, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('returnTable', 3, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('returnTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('returnTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('returnTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('returnTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('returnTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('returnTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>70.56%</strong></td>
                    <td>62.8%</td>
                    <td>2277</td>
                    <td>1.05</td>
                    <td class="negative">32.20%</td>
                    <td class="positive"><strong>0.9833</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h29m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>58.59%</strong></td>
                    <td>70.8%</td>
                    <td>277</td>
                    <td>1.41</td>
                    <td class="negative">12.13%</td>
                    <td class="positive"><strong>0.8366</strong></td>
                    <td class="negative">+0.90% / -1.54%</td>
                    <td>1h48m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>26.72%</strong></td>
                    <td>62.2%</td>
                    <td>1818</td>
                    <td>1.02</td>
                    <td class="negative">40.62%</td>
                    <td class="positive"><strong>0.8314</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>2h24m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>18.82%</strong></td>
                    <td>65.8%</td>
                    <td>269</td>
                    <td>1.12</td>
                    <td class="negative">14.07%</td>
                    <td class="positive"><strong>0.6863</strong></td>
                    <td class="negative">+0.89% / -1.42%</td>
                    <td>2h7m<br><small>(2.0m - 25h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>11.38%</strong></td>
                    <td>66.3%</td>
                    <td>104</td>
                    <td>1.18</td>
                    <td class="negative">6.52%</td>
                    <td class="positive"><strong>0.5932</strong></td>
                    <td class="negative">+0.90% / -1.46%</td>
                    <td>2h48m<br><small>(1.0m - 18h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>9.71%</strong></td>
                    <td>63.3%</td>
                    <td>109</td>
                    <td>1.15</td>
                    <td class="negative">7.43%</td>
                    <td class="positive"><strong>0.5906</strong></td>
                    <td class="negative">+0.90% / -1.36%</td>
                    <td>3h10m<br><small>(1.0m - 23h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>8.59%</strong></td>
                    <td>67.9%</td>
                    <td>28</td>
                    <td>1.63</td>
                    <td class="neutral">3.48%</td>
                    <td class="positive"><strong>0.5380</strong></td>
                    <td class="negative">+0.92% / -1.36%</td>
                    <td>2h44m<br><small>(13.0m - 13h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>5.30%</strong></td>
                    <td>63.1%</td>
                    <td>65</td>
                    <td>1.14</td>
                    <td class="negative">6.58%</td>
                    <td class="positive"><strong>0.5258</strong></td>
                    <td class="negative">+0.95% / -1.41%</td>
                    <td>2h58m<br><small>(1.0m - 28h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>4.07%</strong></td>
                    <td>73.3%</td>
                    <td>15</td>
                    <td>1.60</td>
                    <td class="neutral">2.59%</td>
                    <td class="positive"><strong>0.4691</strong></td>
                    <td class="negative">+0.89% / -1.38%</td>
                    <td>1h26m<br><small>(3.0m - 4h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>3.58%</strong></td>
                    <td>83.3%</td>
                    <td>6</td>
                    <td>2.81</td>
                    <td class="positive">1.87%</td>
                    <td class="positive"><strong>0.4973</strong></td>
                    <td class="negative">+0.84% / -1.46%</td>
                    <td>5h24m<br><small>(13.0m - 28h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>2.02%</strong></td>
                    <td>61.8%</td>
                    <td>55</td>
                    <td>1.06</td>
                    <td class="negative">7.48%</td>
                    <td class="positive"><strong>0.4892</strong></td>
                    <td class="negative">+0.94% / -1.35%</td>
                    <td>2h10m<br><small>(1.0m - 16h46m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>0.26%</strong></td>
                    <td>58.3%</td>
                    <td>12</td>
                    <td>1.03</td>
                    <td class="neutral">4.40%</td>
                    <td class="positive"><strong>0.3043</strong></td>
                    <td class="negative">+1.04% / -1.65%</td>
                    <td>3.4m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="negative"><strong>-1.97%</strong></td>
                    <td>59.3%</td>
                    <td>27</td>
                    <td>0.89</td>
                    <td class="neutral">4.47%</td>
                    <td class="positive"><strong>0.3571</strong></td>
                    <td class="negative">+0.87% / -1.43%</td>
                    <td>2h33m<br><small>(13.0m - 12h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="negative"><strong>-9.06%</strong></td>
                    <td>45.8%</td>
                    <td>24</td>
                    <td>0.57</td>
                    <td class="negative">12.34%</td>
                    <td class="positive"><strong>0.2194</strong></td>
                    <td class="negative">+0.95% / -1.33%</td>
                    <td>1h43m<br><small>(13.0m - 7h10m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="negative"><strong>-14.73%</strong></td>
                    <td>59.3%</td>
                    <td>204</td>
                    <td>0.89</td>
                    <td class="negative">22.18%</td>
                    <td class="positive"><strong>0.5009</strong></td>
                    <td class="negative">+0.86% / -1.43%</td>
                    <td>1h54m<br><small>(1.0m - 33h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="negative"><strong>-62.03%</strong></td>
                    <td>60.8%</td>
                    <td>2212</td>
                    <td>0.96</td>
                    <td class="negative">71.62%</td>
                    <td class="positive"><strong>0.5674</strong></td>
                    <td class="negative">+0.87% / -1.39%</td>
                    <td>2h57m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
                <div>
                    <h3 style="color: #27AE60; text-align: center;">🎯 Ranked by Win Rate</h3>
                    <div class="table-container">
                        
        <table id="winRateTable" class="sortable-table">
            <thead>
                <tr>
                    <th onclick="sortTable('winRateTable', 0, 'number')">Rank ↕</th>
                    <th onclick="sortTable('winRateTable', 1, 'string')">Rule Name ↕</th>
                    <th onclick="sortTable('winRateTable', 2, 'number')">Win Rate (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 3, 'number')">Total Return (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 4, 'number')">Trades ↕</th>
                    <th onclick="sortTable('winRateTable', 5, 'number')">Profit Factor ↕</th>
                    <th onclick="sortTable('winRateTable', 6, 'number')">Max Drawdown (%) ↕</th>
                    <th onclick="sortTable('winRateTable', 7, 'number')">Performance Score ↕</th>
                    <th onclick="sortTable('winRateTable', 8, 'string')">Avg Gain/Loss ↕</th>
                    <th onclick="sortTable('winRateTable', 9, 'string')">Avg Duration ↕</th>
                </tr>
            </thead>
            <tbody>
        
                <tr>
                    <td><strong>#1</strong></td>
                    <td>Rule 27: Structure Break Up</td>
                    <td class="positive"><strong>83.3%</strong></td>
                    <td class="positive">3.58%</td>
                    <td>6</td>
                    <td>2.81</td>
                    <td class="positive">1.87%</td>
                    <td class="positive"><strong>0.4973</strong></td>
                    <td class="negative">+0.84% / -1.46%</td>
                    <td>5h24m<br><small>(13.0m - 28h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#2</strong></td>
                    <td>Rule 2: Golden Cross</td>
                    <td class="positive"><strong>73.3%</strong></td>
                    <td class="positive">4.07%</td>
                    <td>15</td>
                    <td>1.60</td>
                    <td class="neutral">2.59%</td>
                    <td class="positive"><strong>0.4691</strong></td>
                    <td class="negative">+0.89% / -1.38%</td>
                    <td>1h26m<br><small>(3.0m - 4h35m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#3</strong></td>
                    <td>Prof Rule 7: Mean Reversion Volatility Filter</td>
                    <td class="positive"><strong>70.8%</strong></td>
                    <td class="positive">58.59%</td>
                    <td>277</td>
                    <td>1.41</td>
                    <td class="negative">12.13%</td>
                    <td class="positive"><strong>0.8366</strong></td>
                    <td class="negative">+0.90% / -1.54%</td>
                    <td>1h48m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#4</strong></td>
                    <td>Price Action Rule 3: Engulfing Pattern</td>
                    <td class="positive"><strong>67.9%</strong></td>
                    <td class="positive">8.59%</td>
                    <td>28</td>
                    <td>1.63</td>
                    <td class="neutral">3.48%</td>
                    <td class="positive"><strong>0.5380</strong></td>
                    <td class="negative">+0.92% / -1.36%</td>
                    <td>2h44m<br><small>(13.0m - 13h49m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#5</strong></td>
                    <td>Volume Rule 5: Smart Money Volume</td>
                    <td class="positive"><strong>66.3%</strong></td>
                    <td class="positive">11.38%</td>
                    <td>104</td>
                    <td>1.18</td>
                    <td class="negative">6.52%</td>
                    <td class="positive"><strong>0.5932</strong></td>
                    <td class="negative">+0.90% / -1.46%</td>
                    <td>2h48m<br><small>(1.0m - 18h30m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#6</strong></td>
                    <td>Rule 6: Stochastic Oversold Cross</td>
                    <td class="positive"><strong>65.8%</strong></td>
                    <td class="positive">18.82%</td>
                    <td>269</td>
                    <td>1.12</td>
                    <td class="negative">14.07%</td>
                    <td class="positive"><strong>0.6863</strong></td>
                    <td class="negative">+0.89% / -1.42%</td>
                    <td>2h7m<br><small>(2.0m - 25h11m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#7</strong></td>
                    <td>Professional Rule 10: CCI Reversal Enhanced</td>
                    <td class="positive"><strong>63.3%</strong></td>
                    <td class="positive">9.71%</td>
                    <td>109</td>
                    <td>1.15</td>
                    <td class="negative">7.43%</td>
                    <td class="positive"><strong>0.5906</strong></td>
                    <td class="negative">+0.90% / -1.36%</td>
                    <td>3h10m<br><small>(1.0m - 23h57m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#8</strong></td>
                    <td>Momentum Rule 2: Momentum Divergence Recovery</td>
                    <td class="positive"><strong>63.1%</strong></td>
                    <td class="positive">5.30%</td>
                    <td>65</td>
                    <td>1.14</td>
                    <td class="negative">6.58%</td>
                    <td class="positive"><strong>0.5258</strong></td>
                    <td class="negative">+0.95% / -1.41%</td>
                    <td>2h58m<br><small>(1.0m - 28h36m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#9</strong></td>
                    <td>AI Rule 10: Composite Sentiment Reversal</td>
                    <td class="positive"><strong>62.8%</strong></td>
                    <td class="positive">70.56%</td>
                    <td>2277</td>
                    <td>1.05</td>
                    <td class="negative">32.20%</td>
                    <td class="positive"><strong>0.9833</strong></td>
                    <td class="negative">+0.88% / -1.42%</td>
                    <td>2h29m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#10</strong></td>
                    <td>Rule 7: Bollinger Band Bounce</td>
                    <td class="positive"><strong>62.2%</strong></td>
                    <td class="positive">26.72%</td>
                    <td>1818</td>
                    <td>1.02</td>
                    <td class="negative">40.62%</td>
                    <td class="positive"><strong>0.8314</strong></td>
                    <td class="negative">+0.89% / -1.43%</td>
                    <td>2h24m<br><small>(1.0m - 49h1m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#11</strong></td>
                    <td>Professional Rule 7: Chaikin Money Flow Reversal</td>
                    <td class="positive"><strong>61.8%</strong></td>
                    <td class="positive">2.02%</td>
                    <td>55</td>
                    <td>1.06</td>
                    <td class="negative">7.48%</td>
                    <td class="positive"><strong>0.4892</strong></td>
                    <td class="negative">+0.94% / -1.35%</td>
                    <td>2h10m<br><small>(1.0m - 16h46m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#12</strong></td>
                    <td>Ext Rule 6: Fibonacci Support Confluence</td>
                    <td class="positive"><strong>60.8%</strong></td>
                    <td class="negative">-62.03%</td>
                    <td>2212</td>
                    <td>0.96</td>
                    <td class="negative">71.62%</td>
                    <td class="positive"><strong>0.5674</strong></td>
                    <td class="negative">+0.87% / -1.39%</td>
                    <td>2h57m<br><small>(1.0m - 55h53m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#13</strong></td>
                    <td>AI Rule 8: Momentum Divergence Reversal</td>
                    <td class="positive"><strong>59.3%</strong></td>
                    <td class="negative">-14.73%</td>
                    <td>204</td>
                    <td>0.89</td>
                    <td class="negative">22.18%</td>
                    <td class="positive"><strong>0.5009</strong></td>
                    <td class="negative">+0.86% / -1.43%</td>
                    <td>1h54m<br><small>(1.0m - 33h19m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#14</strong></td>
                    <td>Rule 10: Volume Spike</td>
                    <td class="positive"><strong>59.3%</strong></td>
                    <td class="negative">-1.97%</td>
                    <td>27</td>
                    <td>0.89</td>
                    <td class="neutral">4.47%</td>
                    <td class="positive"><strong>0.3571</strong></td>
                    <td class="negative">+0.87% / -1.43%</td>
                    <td>2h33m<br><small>(13.0m - 12h21m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#15</strong></td>
                    <td>Acad Rule 2: Mean Reversion Factor</td>
                    <td class="positive"><strong>58.3%</strong></td>
                    <td class="positive">0.26%</td>
                    <td>12</td>
                    <td>1.03</td>
                    <td class="neutral">4.40%</td>
                    <td class="positive"><strong>0.3043</strong></td>
                    <td class="negative">+1.04% / -1.65%</td>
                    <td>3.4m<br><small>(1.0m - 14.0m)</small></td>
                </tr>
            
                <tr>
                    <td><strong>#16</strong></td>
                    <td>Advanced Rule 7: DMI ADX Filter</td>
                    <td class="neutral"><strong>45.8%</strong></td>
                    <td class="negative">-9.06%</td>
                    <td>24</td>
                    <td>0.57</td>
                    <td class="negative">12.34%</td>
                    <td class="positive"><strong>0.2194</strong></td>
                    <td class="negative">+0.95% / -1.33%</td>
                    <td>1h43m<br><small>(13.0m - 7h10m)</small></td>
                </tr>
            
            </tbody>
        </table>
        
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>📉 Sell Rules Performance</h2>
            <div class="chart-container">
                <div id="sellRulesChart" style="height: 500px;"></div>
            </div>
            <div class="table-container">
                <p>No sell rules data available.</p>
            </div>
        </div>
    </div>
    
    <script>
        // Table Sorting Functionality
        function sortTable(tableId, columnIndex, dataType) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            const currentDirection = table.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            table.setAttribute('data-sort-direction', newDirection);

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                if (dataType === 'number') {
                    // Extract numeric values, handling percentages and special characters
                    aValue = parseFloat(aValue.replace(/[^-0-9.]/g, '')) || 0;
                    bValue = parseFloat(bValue.replace(/[^-0-9.]/g, '')) || 0;

                    return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    // String comparison
                    return newDirection === 'asc'
                        ? aValue.localeCompare(bValue)
                        : bValue.localeCompare(aValue);
                }
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Update rank numbers in first column
            rows.forEach((row, index) => {
                if (row.cells[0].textContent.includes('#')) {
                    row.cells[0].innerHTML = `<strong>#${index + 1}</strong>`;
                }
            });

            // Update header indicators
            const headers = table.querySelectorAll('th');
            headers.forEach((header, index) => {
                const text = header.textContent.replace(' ↑', '').replace(' ↓', '').replace(' ↕', '');
                if (index === columnIndex) {
                    header.textContent = text + (newDirection === 'asc' ? ' ↑' : ' ↓');
                } else {
                    header.textContent = text + ' ↕';
                }
            });
        }

        // Buy Rules Chart
        
        var buyRulesData = [
            {
                x: ['AI Rule 10: Composite Sentimen...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 7: Bollinger Band Bounce', 'Rule 6: Stochastic Oversold Cr...', 'Volume Rule 5: Smart Money Vol...', 'Professional Rule 10: CCI Reve...', 'AI Rule 8: Momentum Divergence...', 'Momentum Rule 2: Momentum Dive...', 'Professional Rule 7: Chaikin M...', 'Price Action Rule 3: Engulfing...', 'Rule 27: Structure Break Up', 'Rule 2: Golden Cross', 'Rule 10: Volume Spike', 'Ext Rule 6: Fibonacci Support ...', 'Acad Rule 2: Mean Reversion Fa...'],
                y: [62.80193236714976, 70.7581227436823, 62.211221122112214, 65.79925650557621, 66.34615384615384, 63.30275229357798, 59.31372549019608, 63.07692307692307, 61.81818181818181, 67.85714285714286, 83.33333333333334, 73.33333333333333, 59.25925925925925, 60.80470162748644, 58.333333333333336],
                name: 'Win Rate (%)',
                type: 'bar',
                marker: {
                    color: 'rgba(46, 134, 171, 0.8)'
                }
            },
            {
                x: ['AI Rule 10: Composite Sentimen...', 'Prof Rule 7: Mean Reversion Vo...', 'Rule 7: Bollinger Band Bounce', 'Rule 6: Stochastic Oversold Cr...', 'Volume Rule 5: Smart Money Vol...', 'Professional Rule 10: CCI Reve...', 'AI Rule 8: Momentum Divergence...', 'Momentum Rule 2: Momentum Dive...', 'Professional Rule 7: Chaikin M...', 'Price Action Rule 3: Engulfing...', 'Rule 27: Structure Break Up', 'Rule 2: Golden Cross', 'Rule 10: Volume Spike', 'Ext Rule 6: Fibonacci Support ...', 'Acad Rule 2: Mean Reversion Fa...'],
                y: [np.float64(70.56063626643778), np.float64(58.589648927075146), np.float64(26.715800557201263), np.float64(18.824565996013074), np.float64(11.37944548443587), np.float64(9.708190689695794), np.float64(-14.731276946229846), np.float64(5.298779238169387), np.float64(2.0178836938778986), np.float64(8.589617328545181), np.float64(3.57564442323726), np.float64(4.074763186611934), np.float64(-1.9725238385936243), np.float64(-62.02704209465825), np.float64(0.260250922211344)],
                name: 'Total Return (%)',
                type: 'bar',
                yaxis: 'y2',
                marker: {
                    color: 'rgba(39, 174, 96, 0.8)'
                }
            }
        ];

        var buyRulesLayout = {
            title: 'Top Buy Rules Performance',
            xaxis: {title: 'Rules', tickangle: -45},
            yaxis: {title: 'Win Rate (%)', side: 'left'},
            yaxis2: {title: 'Total Return (%)', side: 'right', overlaying: 'y'},
            margin: {l: 60, r: 60, t: 60, b: 120}
        };

        Plotly.newPlot('buyRulesChart', buyRulesData, buyRulesLayout, {responsive: true});
        

        // Sell Rules Chart
        console.log('No sell rules data for chart');
    </script>
</body>
</html>
